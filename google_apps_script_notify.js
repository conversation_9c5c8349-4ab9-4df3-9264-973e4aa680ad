/**
 * Google Apps Script - 藍新金流通知處理器
 * 這個腳本將部署為 Web App 來接收藍新金流的付款通知
 */

// === 設定區域 ===
const CONFIG = {
  // 藍新金流設定
  NEWEBPAY_KEY: 'vTxBcUTfd54kxzh01qborPcL5kJtMoxE',
  NEWEBPAY_IV: 'PEEDYyAVozYO6rDC',
  MERCHANT_ID: 'MS3757703701',
  
  // Google Sheets 設定
  SPREADSHEET_ID: '請替換為您的 Google Sheets ID',
  SHEET_NAME: 'donation_records',
  
  // 安全設定
  ALLOWED_IPS: [
    '*************',  // 藍新金流 IP (請確認最新 IP)
    '*************',
    // 可以添加更多允許的 IP
  ],
  
  // 日誌設定
  LOG_SHEET_NAME: 'notification_logs'
};

/**
 * Web App 主要入口點 - 處理 POST 請求
 */
function doPost(e) {
  try {
    // 記錄請求資訊
    logRequest('POST Request Received', {
      parameters: e.parameters,
      postData: e.postData,
      contentLength: e.postData ? e.postData.length : 0
    });
    
    // 驗證請求來源 (可選)
    // if (!isValidSource()) {
    //   return createResponse(403, 'Forbidden');
    // }
    
    // 解析 POST 資料
    const postData = parsePostData(e);
    
    if (!postData.TradeInfo || !postData.TradeSha) {
      logRequest('Invalid Request', 'Missing TradeInfo or TradeSha');
      return createResponse(400, 'Bad Request');
    }
    
    // 驗證和解密資料
    const decryptedData = verifyAndDecryptData(postData.TradeInfo, postData.TradeSha);
    
    if (!decryptedData) {
      logRequest('Verification Failed', 'Invalid signature or decryption failed');
      return createResponse(400, 'Verification Failed');
    }
    
    // 更新 Google Sheets
    const updateResult = updateDonationRecord(decryptedData);
    
    if (updateResult.success) {
      logRequest('Success', `Updated record: ${decryptedData.MerchantOrderNo}`);
      return createResponse(200, 'OK');
    } else {
      logRequest('Update Failed', updateResult.error);
      return createResponse(500, 'Internal Error');
    }
    
  } catch (error) {
    logRequest('Exception', error.toString());
    return createResponse(500, 'Internal Server Error');
  }
}

/**
 * 處理 GET 請求 - 用於測試
 */
function doGet(e) {
  const testMode = e.parameter.test === 'true';
  
  if (testMode) {
    return createResponse(200, JSON.stringify({
      status: 'OK',
      timestamp: new Date().toISOString(),
      message: 'Google Apps Script Webhook is working'
    }));
  }
  
  return createResponse(200, 'Donation Notification Webhook');
}

/**
 * 解析 POST 資料
 */
function parsePostData(e) {
  const postData = {};
  
  if (e.postData && e.postData.contents) {
    // 解析 form-encoded 資料
    const params = e.postData.contents.split('&');
    params.forEach(param => {
      const [key, value] = param.split('=');
      if (key && value) {
        postData[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    });
  }
  
  // 也檢查 parameters (GET 參數格式)
  if (e.parameters) {
    Object.keys(e.parameters).forEach(key => {
      postData[key] = e.parameters[key][0]; // parameters 是陣列格式
    });
  }
  
  return postData;
}

/**
 * 驗證和解密藍新金流資料
 */
function verifyAndDecryptData(tradeInfo, tradeSha) {
  try {
    // 驗證 SHA256 簽章
    const expectedSha = Utilities.computeDigest(
      Utilities.DigestAlgorithm.SHA_256,
      `HashKey=${CONFIG.NEWEBPAY_KEY}&${tradeInfo}&HashIV=${CONFIG.NEWEBPAY_IV}`,
      Utilities.Charset.UTF_8
    );

    const expectedShaHex = expectedSha.map(byte =>
      (byte < 0 ? byte + 256 : byte).toString(16).padStart(2, '0')
    ).join('').toUpperCase();

    if (expectedShaHex !== tradeSha.toUpperCase()) {
      logRequest('SHA Verification Failed', `Expected: ${expectedShaHex}, Got: ${tradeSha}`);
      return null;
    }

    // 解密 AES 資料 - 使用簡化方法
    // 注意：由於 Google Apps Script 的 AES 解密限制，
    // 建議使用 UrlFetchApp 調用外部解密服務或使用替代方案

    // 方案1：直接記錄加密資料，手動處理
    logRequest('Encrypted Data Received', {
      tradeInfo: tradeInfo,
      tradeSha: tradeSha,
      verified: true
    });

    // 方案2：使用外部解密服務 (推薦)
    const decryptedData = callExternalDecryptService(tradeInfo);

    if (decryptedData) {
      return decryptedData;
    }

    // 方案3：假設解密成功，返回基本資料結構
    // 實際部署時需要實現真正的解密
    return {
      Status: 'SUCCESS', // 這裡需要從實際解密資料中取得
      MerchantOrderNo: 'TEMP_ORDER_NO', // 這裡需要從實際解密資料中取得
      Amt: '1000', // 這裡需要從實際解密資料中取得
      // 其他欄位...
    };

  } catch (error) {
    logRequest('Decryption Error', error.toString());
    return null;
  }
}

/**
 * 調用外部解密服務 (推薦方案)
 */
function callExternalDecryptService(tradeInfo) {
  try {
    // 可以調用您的 PHP 服務來解密
    const response = UrlFetchApp.fetch('https://amtbtc.infy.uk/donate/decrypt_service.php', {
      method: 'POST',
      payload: {
        'tradeInfo': tradeInfo,
        'key': CONFIG.NEWEBPAY_KEY,
        'iv': CONFIG.NEWEBPAY_IV
      }
    });

    if (response.getResponseCode() === 200) {
      return JSON.parse(response.getContentText());
    }

  } catch (error) {
    logRequest('External Decrypt Error', error.toString());
  }

  return null;
}

/**
 * 更新 Google Sheets 中的捐款記錄
 */
function updateDonationRecord(data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(CONFIG.SHEET_NAME);
    
    if (!sheet) {
      return { success: false, error: 'Sheet not found' };
    }
    
    // 查找對應的訂單記錄
    const dataRange = sheet.getDataRange();
    const values = dataRange.getValues();
    
    // 假設訂單編號在 B 欄 (索引 1)
    let rowIndex = -1;
    for (let i = 1; i < values.length; i++) { // 跳過標題列
      if (values[i][1] === data.MerchantOrderNo) {
        rowIndex = i + 1; // Google Sheets 行號從 1 開始
        break;
      }
    }
    
    if (rowIndex === -1) {
      return { success: false, error: 'Order not found' };
    }
    
    // 更新狀態和相關資訊
    const status = data.Status === 'SUCCESS' ? 'Completed' : 'Failed';
    const updateTime = new Date();
    const responseData = JSON.stringify(data);
    
    // 更新對應欄位 (根據您的 Google Sheets 結構)
    sheet.getRange(rowIndex, 15).setValue(status);        // O 欄：狀態
    sheet.getRange(rowIndex, 18).setValue(responseData);  // R 欄：藍新回應
    sheet.getRange(rowIndex, 19).setValue(updateTime);   // S 欄：更新時間
    
    return { success: true };
    
  } catch (error) {
    return { success: false, error: error.toString() };
  }
}

/**
 * 記錄請求日誌
 */
function logRequest(type, data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    let logSheet = spreadsheet.getSheetByName(CONFIG.LOG_SHEET_NAME);
    
    // 如果日誌工作表不存在，建立它
    if (!logSheet) {
      logSheet = spreadsheet.insertSheet(CONFIG.LOG_SHEET_NAME);
      logSheet.getRange(1, 1, 1, 5).setValues([
        ['Timestamp', 'Type', 'Data', 'IP', 'User Agent']
      ]);
    }
    
    // 添加日誌記錄
    logSheet.appendRow([
      new Date(),
      type,
      typeof data === 'object' ? JSON.stringify(data) : data,
      Session.getTemporaryActiveUserKey(), // 替代 IP 地址
      'Google Apps Script'
    ]);
    
  } catch (error) {
    console.error('Logging failed:', error);
  }
}

/**
 * 建立 HTTP 回應
 */
function createResponse(code, message) {
  return ContentService
    .createTextOutput(message)
    .setMimeType(ContentService.MimeType.TEXT);
}

/**
 * 驗證請求來源 (可選功能)
 */
function isValidSource() {
  // Google Apps Script 中較難取得真實 IP
  // 可以考慮其他驗證方式，如時間戳驗證、額外的密鑰等
  return true;
}

/**
 * 測試函數 - 用於開發階段測試
 */
function testWebhook() {
  const testData = {
    postData: {
      contents: 'TradeInfo=test_encrypted_data&TradeSha=test_hash_value'
    }
  };
  
  const result = doPost(testData);
  console.log('Test result:', result.getContent());
}
