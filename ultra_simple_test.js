/**
 * 極簡測試版本 - Code.gs
 * 最基本的 POST 功能測試
 */

const SIMPLE_CONFIG = {
  SPREADSHEET_ID: '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc',
  LOG_SHEET_NAME: 'system_logs'
};

/**
 * 處理 GET 請求
 */
function doGet(e) {
  const action = e.parameter.action || 'form';
  
  if (action === 'simpletest') {
    return handleSimpleTest();
  } else {
    return showUltraSimpleForm();
  }
}

/**
 * 處理 POST 請求
 */
function doPost(e) {
  try {
    // 步驟 1: 記錄 POST 開始
    simpleLog('POST_STEP_1', 'POST request started');
    
    // 步驟 2: 檢查參數
    if (!e || !e.parameters) {
      simpleLog('POST_STEP_2_FAIL', 'No parameters');
      return createSimpleResponse('錯誤：沒有參數');
    }
    simpleLog('POST_STEP_2_OK', 'Parameters exist');
    
    // 步驟 3: 提取參數
    const name = e.parameters.donor_name ? e.parameters.donor_name[0] : '';
    const email = e.parameters.donor_email ? e.parameters.donor_email[0] : '';
    simpleLog('POST_STEP_3_OK', `Name: ${name}, Email: ${email}`);
    
    // 步驟 4: 基本驗證
    if (!name || !email) {
      simpleLog('POST_STEP_4_FAIL', 'Missing name or email');
      return createSimpleResponse('錯誤：缺少姓名或信箱');
    }
    simpleLog('POST_STEP_4_OK', 'Validation passed');
    
    // 步驟 5: 嘗試寫入 Google Sheets
    try {
      const spreadsheet = SpreadsheetApp.openById(SIMPLE_CONFIG.SPREADSHEET_ID);
      simpleLog('POST_STEP_5A_OK', 'Spreadsheet opened');
      
      const sheet = spreadsheet.getSheetByName('donation_records');
      if (!sheet) {
        simpleLog('POST_STEP_5B_FAIL', 'Sheet not found');
        return createSimpleResponse('錯誤：找不到工作表');
      }
      simpleLog('POST_STEP_5B_OK', 'Sheet found');
      
      // 簡單記錄
      const record = [
        new Date().toLocaleString('zh-TW'),
        'ULTRA_SIMPLE_' + Date.now(),
        1500,
        name,
        email,
        '',
        'ULTRA_SIMPLE_TEST',
        'test',
        1000, 500, 0, 0, 0, 0,
        'Ultra Simple Test',
        '', '', '',
        new Date().toLocaleString('zh-TW'),
        'Ultra simple test record'
      ];
      
      sheet.appendRow(record);
      simpleLog('POST_STEP_5C_OK', 'Record written to sheet');
      
    } catch (sheetError) {
      simpleLog('POST_STEP_5_ERROR', sheetError.toString());
      return createSimpleResponse('錯誤：寫入失敗 - ' + sheetError.toString());
    }
    
    // 步驟 6: 返回成功
    simpleLog('POST_STEP_6_OK', 'All steps completed successfully');
    return createSimpleResponse('成功：POST 測試完成！訂單：ULTRA_SIMPLE_' + Date.now());
    
  } catch (error) {
    simpleLog('POST_EXCEPTION', error.toString());
    return createSimpleResponse('異常：' + error.toString());
  }
}

/**
 * 顯示極簡表單
 */
function showUltraSimpleForm() {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>極簡測試</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial; padding: 20px; background: #f9f9f9; }
        .container { max-width: 400px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; }
        input { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box; }
        button { width: 100%; padding: 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; margin: 5px 0; }
        button:hover { background: #0056b3; }
        .info { background: #e7f3ff; padding: 10px; border-radius: 3px; margin: 10px 0; }
        .test-link { display: block; background: #28a745; color: white; text-align: center; padding: 8px; text-decoration: none; border-radius: 3px; margin: 5px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <h2>🔬 極簡 POST 測試</h2>
        
        <div class="info">
          <strong>目標：</strong>找出 POST 失敗的確切原因<br>
          <strong>時間：</strong>${new Date().toLocaleString('zh-TW')}
        </div>
        
        <form method="post">
          <input type="text" name="donor_name" placeholder="姓名" value="極簡測試用戶" required>
          <input type="email" name="donor_email" placeholder="信箱" value="<EMAIL>" required>
          <button type="submit">提交極簡測試</button>
        </form>
        
        <a href="?simpletest=true" class="test-link">直接測試 POST 功能</a>
        
        <div class="info">
          <strong>說明：</strong><br>
          1. 先點擊「直接測試 POST 功能」<br>
          2. 再填寫表單提交<br>
          3. 檢查 system_logs 中的詳細步驟記錄
        </div>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}

/**
 * 簡單測試處理
 */
function handleSimpleTest() {
  try {
    simpleLog('SIMPLE_TEST_START', 'Direct POST test started');
    
    // 模擬 POST 事件
    const mockEvent = {
      parameters: {
        donor_name: ['直接測試用戶'],
        donor_email: ['<EMAIL>']
      }
    };
    
    simpleLog('SIMPLE_TEST_MOCK', 'Mock event created');
    
    // 直接調用 doPost
    const result = doPost(mockEvent);
    
    simpleLog('SIMPLE_TEST_COMPLETE', 'doPost call completed');
    
    return result;
    
  } catch (error) {
    simpleLog('SIMPLE_TEST_ERROR', error.toString());
    return createSimpleResponse('直接測試錯誤：' + error.toString());
  }
}

/**
 * 簡單日誌記錄
 */
function simpleLog(step, message) {
  try {
    const spreadsheet = SpreadsheetApp.openById(SIMPLE_CONFIG.SPREADSHEET_ID);
    const logSheet = spreadsheet.getSheetByName(SIMPLE_CONFIG.LOG_SHEET_NAME);
    
    logSheet.appendRow([
      new Date(),
      'ULTRA_SIMPLE: ' + step,
      message,
      'Ultra Simple Session'
    ]);
    
  } catch (error) {
    console.error('Simple logging failed:', error);
  }
}

/**
 * 建立簡單回應
 */
function createSimpleResponse(message) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>極簡測試結果</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial; padding: 20px; background: #f9f9f9; text-align: center; }
        .container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 5px; }
        .result { font-size: 18px; margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .button { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; margin: 10px; }
      </style>
    </head>
    <body>
      <div class="container">
        <h2>🔬 極簡測試結果</h2>
        
        <div class="result ${message.includes('成功') ? 'success' : 'error'}">
          ${message}
        </div>
        
        <div style="margin: 20px 0;">
          <strong>時間：</strong>${new Date().toLocaleString('zh-TW')}
        </div>
        
        <a href="?" class="button">返回測試</a>
        <a href="https://docs.google.com/spreadsheets/d/${SIMPLE_CONFIG.SPREADSHEET_ID}/edit" class="button" target="_blank">查看 Sheets</a>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}
