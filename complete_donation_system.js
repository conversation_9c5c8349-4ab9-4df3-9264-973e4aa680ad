/**
 * 完整捐款系統 - Code.gs
 * 慈光圖書館線上捐款系統
 */

// === 系統設定 ===
const DONATION_CONFIG = {
  SPREADSHEET_ID: '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc',
  SHEET_NAME: 'donation_records',
  LOG_SHEET_NAME: 'system_logs',
  
  // 藍新金流設定
  NEWEBPAY: {
    KEY: 'vTxBcUTfd54kxzh01qborPcL5kJtMoxE',
    IV: 'PEEDYyAVozYO6rDC',
    MERCHANT_ID: 'MS3757703701',
    VERSION: '2.0',
    GATEWAY_URL: 'https://core.newebpay.com/MPG/mpg_gateway'
  }
};

/**
 * 處理 GET 請求
 */
function doGet(e) {
  try {
    const action = e.parameter.action || 'form';
    
    logOperation('GET Request', { action: action, parameters: e.parameters });
    
    switch (action) {
      case 'form':
        return showDonationForm();
      case 'test':
        return handleSystemTest();
      case 'return':
        return handlePaymentReturn(e);
      default:
        return showDonationForm();
    }
    
  } catch (error) {
    logOperation('GET Error', error.toString());
    return createErrorPage('系統錯誤，請稍後再試');
  }
}

/**
 * 處理 POST 請求
 */
function doPost(e) {
  try {
    const action = e.parameter.action || 'donate';
    
    logOperation('POST Request', { action: action });
    
    switch (action) {
      case 'donate':
        return handleDonationSubmission(e);
      case 'notify':
        return handlePaymentNotification(e);
      default:
        return createErrorPage('未知的操作');
    }
    
  } catch (error) {
    logOperation('POST Error', error.toString());
    return createErrorPage('處理請求時發生錯誤');
  }
}

/**
 * 顯示捐款表單
 */
function showDonationForm() {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>慈光圖書館 - 線上捐款</title>
      <style>
        body { font-family: 'Microsoft JhengHei', Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; font-size: 2.5em; }
        .header p { color: #7f8c8d; font-size: 1.1em; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: bold; color: #34495e; }
        .form-group input, .form-group select { width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; transition: border-color 0.3s; }
        .form-group input:focus, .form-group select:focus { border-color: #3498db; outline: none; }
        .donation-items { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 25px; border-radius: 10px; margin: 25px 0; }
        .donation-items h3 { color: #2c3e50; margin-bottom: 20px; text-align: center; }
        .donation-item { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 10px; background: white; border-radius: 8px; }
        .donation-item label { margin-bottom: 0; flex: 1; font-weight: 500; }
        .donation-item input { width: 150px; margin-left: 15px; }
        .total-amount { background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; font-size: 24px; font-weight: bold; margin: 20px 0; }
        .submit-btn { background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 18px 40px; border: none; border-radius: 25px; font-size: 20px; font-weight: bold; cursor: pointer; width: 100%; transition: transform 0.3s; }
        .submit-btn:hover { transform: translateY(-2px); }
        .required { color: #e74c3c; }
        .info-box { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 8px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🏛️ 慈光圖書館</h1>
          <h2>線上捐款系統</h2>
          <p>感謝您的護持，您的每一分善款都將用於弘法利生</p>
        </div>
        
        <form id="donationForm" method="post">
          <input type="hidden" name="action" value="donate">
          
          <div class="form-group">
            <label for="donor_name">捐款人姓名 <span class="required">*</span></label>
            <input type="text" id="donor_name" name="donor_name" required placeholder="請輸入您的姓名">
          </div>
          
          <div class="form-group">
            <label for="donor_email">電子郵件 <span class="required">*</span></label>
            <input type="email" id="donor_email" name="donor_email" required placeholder="<EMAIL>">
          </div>
          
          <div class="form-group">
            <label for="donor_phone">聯絡電話</label>
            <input type="tel" id="donor_phone" name="donor_phone" placeholder="09xxxxxxxx">
          </div>
          
          <div class="donation-items">
            <h3>🙏 護持項目</h3>
            <div class="donation-item">
              <label for="item_general">一般經費</label>
              <input type="number" id="item_general" name="item_amount_general_expenses" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_book">助印弘法</label>
              <input type="number" id="item_book" name="item_amount_book_printing" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_life">護生關懷助念</label>
              <input type="number" id="item_life" name="item_amount_life_release" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_clinic">義診慈善救濟</label>
              <input type="number" id="item_clinic" name="item_amount_free_clinic" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_building">建築修繕設備</label>
              <input type="number" id="item_building" name="item_amount_building_repair" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_jewels">供養三寶</label>
              <input type="number" id="item_jewels" name="item_amount_offering_three_jewels" min="0" value="0" onchange="calculateTotal()">
            </div>
          </div>
          
          <div class="total-amount">
            💰 總捐款金額：NT$ <span id="totalAmount">0</span>
          </div>
          
          <div class="form-group">
            <label for="receipt_option">收據選項</label>
            <select id="receipt_option" name="receipt_option">
              <option value="no_receipt">不需要收據</option>
              <option value="monthly_receipt">依次寄發收據</option>
              <option value="annual_receipt">年度收據</option>
            </select>
          </div>
          
          <div class="info-box">
            <p><strong>📋 付款說明：</strong></p>
            <ul>
              <li>支援信用卡、銀聯卡、超商條碼、LINE Pay、Google Pay 等多種付款方式</li>
              <li>所有交易均透過藍新金流安全加密處理</li>
              <li>捐款收據將依您的選擇寄發</li>
            </ul>
          </div>
          
          <button type="submit" class="submit-btn">🙏 確認捐款</button>
        </form>
      </div>
      
      <script>
        function calculateTotal() {
          const items = [
            'item_amount_general_expenses',
            'item_amount_book_printing', 
            'item_amount_life_release',
            'item_amount_free_clinic',
            'item_amount_building_repair',
            'item_amount_offering_three_jewels'
          ];
          
          let total = 0;
          items.forEach(item => {
            const value = parseInt(document.getElementsByName(item)[0].value) || 0;
            total += value;
          });
          
          document.getElementById('totalAmount').textContent = total.toLocaleString();
        }
        
        document.getElementById('donationForm').addEventListener('submit', function(e) {
          const total = parseInt(document.getElementById('totalAmount').textContent.replace(/,/g, ''));
          if (total <= 0) {
            e.preventDefault();
            alert('請輸入捐款金額');
            return false;
          }
          
          // 顯示處理中訊息
          const submitBtn = document.querySelector('.submit-btn');
          submitBtn.innerHTML = '⏳ 處理中，請稍候...';
          submitBtn.disabled = true;
        });
        
        // 初始計算
        calculateTotal();
      </script>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html)
    .setTitle('慈光圖書館線上捐款')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * 處理捐款表單提交
 */
function handleDonationSubmission(e) {
  try {
    // 解析表單資料
    const formData = {};
    Object.keys(e.parameters).forEach(key => {
      formData[key] = e.parameters[key][0];
    });
    
    // 驗證必要欄位
    if (!formData.donor_name || !formData.donor_email) {
      return createErrorPage('請填寫必要資訊');
    }
    
    // 計算總金額
    const totalAmount = calculateDonationAmount(formData);
    if (totalAmount <= 0) {
      return createErrorPage('捐款金額必須大於零');
    }
    
    // 生成訂單編號
    const merchantOrderNo = 'CDL' + Date.now() + Math.floor(Math.random() * 1000);
    
    // 儲存到 Google Sheets
    const donationRecord = createDonationRecord(formData, merchantOrderNo, totalAmount);
    saveDonationRecord(donationRecord);
    
    // 準備藍新金流資料
    const paymentData = prepareNewebPayData(formData, merchantOrderNo, totalAmount);
    const encryptedData = encryptPaymentData(paymentData);
    
    // 返回付款表單
    return createPaymentForm(encryptedData);
    
  } catch (error) {
    logOperation('Donation Submission Error', error.toString());
    return createErrorPage('處理捐款時發生錯誤');
  }
}

/**
 * 計算捐款總金額
 */
function calculateDonationAmount(formData) {
  const amounts = [
    'item_amount_general_expenses',
    'item_amount_book_printing',
    'item_amount_life_release',
    'item_amount_free_clinic',
    'item_amount_building_repair',
    'item_amount_offering_three_jewels'
  ];
  
  let total = 0;
  amounts.forEach(field => {
    const amount = parseInt(formData[field]) || 0;
    total += amount;
  });
  
  return total;
}

/**
 * 建立捐款記錄
 */
function createDonationRecord(formData, merchantOrderNo, amount) {
  return [
    new Date().toLocaleString('zh-TW'),
    merchantOrderNo,
    amount,
    formData.donor_name || '',
    formData.donor_email || '',
    formData.donor_phone || '',
    '',  // payment_type
    formData.receipt_option || '',
    parseInt(formData.item_amount_general_expenses) || 0,
    parseInt(formData.item_amount_book_printing) || 0,
    parseInt(formData.item_amount_life_release) || 0,
    parseInt(formData.item_amount_free_clinic) || 0,
    parseInt(formData.item_amount_building_repair) || 0,
    parseInt(formData.item_amount_offering_three_jewels) || 0,
    'Pending',
    '',  // trade_info_sent
    '',  // trade_sha_sent
    '',  // newebpay_response
    new Date().toLocaleString('zh-TW'),
    ''   // notes
  ];
}

/**
 * 儲存捐款記錄
 */
function saveDonationRecord(record) {
  try {
    const spreadsheet = SpreadsheetApp.openById(DONATION_CONFIG.SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(DONATION_CONFIG.SHEET_NAME);
    sheet.appendRow(record);
    
    logOperation('Record Saved', { 
      merchant_order_no: record[1], 
      amount: record[2] 
    });
    
  } catch (error) {
    logOperation('Save Record Error', error.toString());
  }
}

/**
 * 記錄系統操作
 */
function logOperation(type, data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(DONATION_CONFIG.SPREADSHEET_ID);
    let logSheet = spreadsheet.getSheetByName(DONATION_CONFIG.LOG_SHEET_NAME);
    
    logSheet.appendRow([
      new Date(),
      type,
      typeof data === 'object' ? JSON.stringify(data) : data,
      Session.getActiveUser().getEmail()
    ]);
    
  } catch (error) {
    console.error('Logging failed:', error);
  }
}

/**
 * 系統測試
 */
function handleSystemTest() {
  try {
    const spreadsheet = SpreadsheetApp.openById(DONATION_CONFIG.SPREADSHEET_ID);
    
    const result = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      spreadsheet_access: true,
      spreadsheet_name: spreadsheet.getName(),
      sheets_count: spreadsheet.getSheets().length,
      sheet_names: spreadsheet.getSheets().map(sheet => sheet.getName()),
      message: '完整系統測試成功'
    };
    
    return ContentService
      .createTextOutput(JSON.stringify(result, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    const errorResult = {
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.toString()
    };
    
    return ContentService
      .createTextOutput(JSON.stringify(errorResult, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 建立錯誤頁面
 */
function createErrorPage(message) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>錯誤</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f8f9fa; }
        .error-container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error { color: #dc3545; font-size: 18px; margin: 20px 0; }
        .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
      </style>
    </head>
    <body>
      <div class="error-container">
        <h2>⚠️ 系統提示</h2>
        <p class="error">${message}</p>
        <a href="?" class="button">返回捐款頁面</a>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}

// 其他函數將在下一個檔案中提供...
