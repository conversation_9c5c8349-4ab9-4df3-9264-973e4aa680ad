<?php
// process_donation_sheets.php - 使用 Google Sheets 作為資料庫的捐款處理腳本

// === Google Sheets API 設定 ===
// 您需要從 Google Cloud Console 取得這些資訊
$GOOGLE_SHEETS_CONFIG = [
    'spreadsheet_id' => '請替換為您的 Google Sheets ID',
    'service_account_key_file' => 'service-account-key.json', // 服務帳戶金鑰檔案
    'sheet_name' => 'donation_records'
];

// === 藍新金流設定 ===
$key = "vTxBcUTfd54kxzh01qborPcL5kJtMoxE";
$iv = "PEEDYyAVozYO6rDC";
$mid = "MS3757703701";

// 引入 Google Sheets API 函數
require_once 'google_sheets_helper.php';

// 確保是 POST 請求
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // === 取得表單資料 ===
    $donor_name = $_POST["donor_name"] ?? '';
    $donor_email = $_POST["donor_email"] ?? '';
    $donor_phone = $_POST["donor_phone"] ?? '';
    $payment_type_preference = $_POST["payment_type"] ?? '';
    $receipt_option = $_POST["receipt_option"] ?? '';

    // 計算各項目金額
    $amount_general = isset($_POST["item_amount_general_expenses"]) ? intval($_POST["item_amount_general_expenses"]) : 0;
    $amount_book = isset($_POST["item_amount_book_printing"]) ? intval($_POST["item_amount_book_printing"]) : 0;
    $amount_life = isset($_POST["item_amount_life_release"]) ? intval($_POST["item_amount_life_release"]) : 0;
    $amount_clinic = isset($_POST["item_amount_free_clinic"]) ? intval($_POST["item_amount_free_clinic"]) : 0;
    $amount_building = isset($_POST["item_amount_building_repair"]) ? intval($_POST["item_amount_building_repair"]) : 0;
    $amount_jewels = isset($_POST["item_amount_offering_three_jewels"]) ? intval($_POST["item_amount_offering_three_jewels"]) : 0;

    $amount = $amount_general + $amount_book + $amount_life + $amount_clinic + $amount_building + $amount_jewels;

    // 驗證金額
    if ($amount <= 0) {
        die("捐款金額必須大於零。");
    }

    // 生成唯一訂單編號
    $merchantOrderNo = "CDL" . time() . mt_rand(100, 999);
    $itemDesc = '慈光圖書館捐款';

    // === 設定通知 URL ===
    // 使用 Google Apps Script 作為通知端點
    $notifyURL = 'https://script.google.com/macros/s/您的Google Apps Script ID/exec';
    $returnURL = 'https://amtbtc.infy.uk/donate/return_url.php';

    // === 準備藍新金流資料 ===
    $data = array(
        'MerchantID' => $mid,
        'RespondType' => 'String',
        'TimeStamp' => time(),
        'Version' => '2.0',
        'MerchantOrderNo' => $merchantOrderNo,
        'Amt' => $amount,
        'ItemDesc' => $itemDesc,
        'NotifyURL' => $notifyURL,
        'ReturnURL' => $returnURL,
        'Email' => $donor_email,
        'EmailModify' => 1,
        'LoginType' => 0,
        // 啟用所有付款方式
        'CREDIT' => 1,
        'UNIONPAY' => 1,
        'CVS' => 1,
        'TAIWANPAY' => 1,
        'LINEPAY' => 1,
        'GOOGLEPAY' => 1,
        'WEBATM' => 1,
        'VACC' => 1,
        'FOREIGN' => 1,
    );

    // === 加密資料 ===
    $data_query_string = http_build_query($data);
    $tradeInfo = bin2hex(openssl_encrypt($data_query_string, "AES-256-CBC", $key, OPENSSL_RAW_DATA, $iv));
    $tradeSha = strtoupper(hash("sha256", "HashKey=" . $key . "&" . $tradeInfo . "&HashIV=" . $iv));

    // === 記錄到 Google Sheets ===
    try {
        $donationRecord = [
            'timestamp' => date('Y-m-d H:i:s'),
            'merchant_order_no' => $merchantOrderNo,
            'amount' => $amount,
            'donor_name' => $donor_name,
            'donor_email' => $donor_email,
            'donor_phone' => $donor_phone,
            'payment_type' => $payment_type_preference,
            'receipt_option' => $receipt_option,
            'item_general' => $amount_general,
            'item_book' => $amount_book,
            'item_life' => $amount_life,
            'item_clinic' => $amount_clinic,
            'item_building' => $amount_building,
            'item_jewels' => $amount_jewels,
            'status' => 'Pending',
            'trade_info_sent' => $tradeInfo,
            'trade_sha_sent' => $tradeSha,
            'newebpay_response' => '',
            'updated_at' => date('Y-m-d H:i:s'),
            'notes' => ''
        ];

        $result = addDonationRecord($donationRecord);
        
        if (!$result['success']) {
            // 記錄錯誤但不中斷流程
            $logFile = 'sheets_error_log.txt';
            $logMessage = "Google Sheets error at: " . date('Y-m-d H:i:s') . "\n";
            $logMessage .= "MerchantOrderNo: " . $merchantOrderNo . "\n";
            $logMessage .= "Error: " . $result['error'] . "\n";
            file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
            
            // 可以選擇繼續處理或中斷
            // die("處理您的捐款時發生內部錯誤，請聯繫網站管理員。");
        }

    } catch (Exception $e) {
        $logFile = 'sheets_error_log.txt';
        $logMessage = "Exception at: " . date('Y-m-d H:i:s') . "\n";
        $logMessage .= "MerchantOrderNo: " . $merchantOrderNo . "\n";
        $logMessage .= "Exception: " . $e->getMessage() . "\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }

    // === 記錄發送資料 ===
    $logDataFile = 'newebpay_data_log.txt';
    $logMessage = "Data to Newebpay at: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "MerchantOrderNo: " . $merchantOrderNo . "\n";
    $logMessage .= "Amount: " . $amount . "\n";
    $logMessage .= "NotifyURL: " . $notifyURL . "\n";
    $logMessage .= "ReturnURL: " . $returnURL . "\n";
    $logMessage .= "Original Data String: " . $data_query_string . "\n";
    $logMessage .= "TradeInfo (Encrypted): " . $tradeInfo . "\n";
    $logMessage .= "TradeSha (Hashed): " . $tradeSha . "\n";
    $logMessage .= "----------------------------------------\n";
    file_put_contents($logDataFile, $logMessage, FILE_APPEND | LOCK_EX);

    // === 自動提交表單到藍新金流 ===
?>
<!DOCTYPE html>
<html>
<head>
    <title>導向支付頁面中...</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .loading { font-size: 18px; color: #666; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 20px auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="spinner"></div>
    <p class="loading">正在安全導向支付頁面，請稍候...</p>
    <p>訂單編號：<?php echo htmlspecialchars($merchantOrderNo); ?></p>

    <form id="newebpay-form" method="post" action="https://core.newebpay.com/MPG/mpg_gateway">
        <input type="hidden" name="MerchantID" value="<?php echo htmlspecialchars($mid); ?>">
        <input type="hidden" name="Version" value="<?php echo htmlspecialchars($data['Version']); ?>">
        <input type="hidden" name="TradeInfo" value="<?php echo htmlspecialchars($tradeInfo); ?>">
        <input type="hidden" name="TradeSha" value="<?php echo htmlspecialchars($tradeSha); ?>">
    </form>

    <script>
        // 3秒後自動提交，給用戶時間看到訂單編號
        setTimeout(function() {
            document.getElementById("newebpay-form").submit();
        }, 3000);
        
        // 也可以立即提交
        // document.getElementById("newebpay-form").submit();
    </script>
</body>
</html>

<?php

} else {
    // 如果不是 POST 請求，重導向到表單頁面
    header("Location: donate.html");
    exit();
}

?>
