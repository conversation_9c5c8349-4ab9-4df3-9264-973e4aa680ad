<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google 服務捐款系統測試工具</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #3498db;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 14px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn.success {
            background: #27ae60;
        }
        
        .btn.danger {
            background: #e74c3c;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Google 服務捐款系統測試工具</h1>
            <p>這個工具可以幫助您測試基於 Google Apps Script 和 Google Sheets 的捐款系統</p>
        </div>
        
        <!-- 系統設定 -->
        <div class="test-section">
            <h3>🔧 系統設定</h3>
            <div class="form-group">
                <label for="gasUrl">Google Apps Script Web App URL:</label>
                <input type="url" id="gasUrl" value="https://script.google.com/macros/s/AKfycbwTKT0l7fUwn0p4prFAjH1m7a1O4RVsz7Up-Du6uJSjN1tkxqZT8S1U8XtT4Hkcb-IK/exec" placeholder="https://script.google.com/macros/s/您的ID/exec">
            </div>
            <div class="form-group">
                <label for="sheetsId">Google Sheets ID:</label>
                <input type="text" id="sheetsId" value="1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc" placeholder="您的 Google Sheets ID">
            </div>
            <button class="btn" onclick="saveConfig()">儲存設定</button>
            <button class="btn" onclick="loadConfig()">載入設定</button>
        </div>
        
        <!-- 基本連接測試 -->
        <div class="test-section">
            <h3>🔗 基本連接測試</h3>
            <p>測試 Google Apps Script 是否正常運作</p>
            <button class="btn" onclick="testConnection()">測試連接</button>
            <button class="btn" onclick="testSystemStatus()">系統狀態檢查</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 捐款表單測試 -->
        <div class="test-section">
            <h3>📝 捐款表單測試</h3>
            <p>測試捐款表單是否正常顯示和提交</p>
            <button class="btn" onclick="testDonationForm()">開啟捐款表單</button>
            <button class="btn" onclick="testFormSubmission()">測試表單提交</button>
            <div id="formResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 資料庫操作測試 -->
        <div class="test-section">
            <h3>💾 資料庫操作測試</h3>
            <p>測試 Google Sheets 資料庫的讀寫功能</p>
            <button class="btn" onclick="testDatabaseWrite()">測試寫入</button>
            <button class="btn" onclick="testDatabaseRead()">測試讀取</button>
            <button class="btn" onclick="testDatabaseUpdate()">測試更新</button>
            <div id="databaseResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 藍新金流整合測試 -->
        <div class="test-section">
            <h3>💳 藍新金流整合測試</h3>
            <p>測試與藍新金流的整合功能</p>
            <button class="btn" onclick="testEncryption()">測試加密功能</button>
            <button class="btn" onclick="testNotifyEndpoint()">測試通知端點</button>
            <button class="btn" onclick="simulatePayment()">模擬付款流程</button>
            <div id="paymentResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 完整流程測試 -->
        <div class="test-section">
            <h3>🔄 完整流程測試</h3>
            <p>執行端到端的完整測試</p>
            <button class="btn success" onclick="runFullTest()">執行完整測試</button>
            <button class="btn danger" onclick="clearAllResults()">清除所有結果</button>
            <div id="fullTestResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 測試結果總覽 -->
        <div class="test-section">
            <h3>📊 測試結果總覽</h3>
            <div id="testSummary">
                <p>尚未執行測試</p>
            </div>
        </div>
    </div>
    
    <script>
        // 全域變數
        let config = {
            gasUrl: '',
            sheetsId: ''
        };
        
        let testResults = {
            connection: null,
            form: null,
            database: null,
            payment: null,
            fullTest: null
        };
        
        // 儲存設定
        function saveConfig() {
            config.gasUrl = document.getElementById('gasUrl').value;
            config.sheetsId = document.getElementById('sheetsId').value;
            
            localStorage.setItem('donationTestConfig', JSON.stringify(config));
            showResult('connectionResult', '設定已儲存', 'success');
        }
        
        // 載入設定
        function loadConfig() {
            const saved = localStorage.getItem('donationTestConfig');
            if (saved) {
                config = JSON.parse(saved);
                document.getElementById('gasUrl').value = config.gasUrl;
                document.getElementById('sheetsId').value = config.sheetsId;
                showResult('connectionResult', '設定已載入', 'success');
            } else {
                showResult('connectionResult', '沒有找到儲存的設定', 'error');
            }
        }
        
        // 測試連接
        async function testConnection() {
            if (!config.gasUrl) {
                showResult('connectionResult', '請先設定 Google Apps Script URL', 'error');
                return;
            }
            
            try {
                showResult('connectionResult', '正在測試連接...', 'info');
                
                const response = await fetch(config.gasUrl);
                const text = await response.text();
                
                if (response.ok) {
                    testResults.connection = true;
                    showResult('connectionResult', `連接成功！\n狀態碼: ${response.status}\n回應: ${text.substring(0, 200)}...`, 'success');
                } else {
                    testResults.connection = false;
                    showResult('connectionResult', `連接失敗！\n狀態碼: ${response.status}\n錯誤: ${text}`, 'error');
                }
            } catch (error) {
                testResults.connection = false;
                showResult('connectionResult', `連接錯誤: ${error.message}`, 'error');
            }
            
            updateTestSummary();
        }
        
        // 測試系統狀態
        async function testSystemStatus() {
            if (!config.gasUrl) {
                showResult('connectionResult', '請先設定 Google Apps Script URL', 'error');
                return;
            }
            
            try {
                showResult('connectionResult', '正在檢查系統狀態...', 'info');
                
                const response = await fetch(`${config.gasUrl}?test=true`);
                const result = await response.json();
                
                if (response.ok) {
                    showResult('connectionResult', `系統狀態檢查完成：\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult('connectionResult', `系統狀態檢查失敗: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('connectionResult', `系統狀態檢查錯誤: ${error.message}`, 'error');
            }
        }
        
        // 測試捐款表單
        function testDonationForm() {
            if (!config.gasUrl) {
                showResult('formResult', '請先設定 Google Apps Script URL', 'error');
                return;
            }
            
            // 在新視窗開啟捐款表單
            window.open(config.gasUrl, '_blank');
            showResult('formResult', '捐款表單已在新視窗開啟，請檢查是否正常顯示', 'info');
        }
        
        // 測試表單提交
        async function testFormSubmission() {
            if (!config.gasUrl) {
                showResult('formResult', '請先設定 Google Apps Script URL', 'error');
                return;
            }
            
            const testData = new FormData();
            testData.append('action', 'donate');
            testData.append('donor_name', '測試用戶');
            testData.append('donor_email', '<EMAIL>');
            testData.append('donor_phone', '0912345678');
            testData.append('item_amount_general_expenses', '1000');
            testData.append('item_amount_book_printing', '500');
            testData.append('receipt_option', 'no_receipt');
            
            try {
                showResult('formResult', '正在測試表單提交...', 'info');
                
                const response = await fetch(config.gasUrl, {
                    method: 'POST',
                    body: testData
                });
                
                const text = await response.text();
                
                if (response.ok) {
                    testResults.form = true;
                    showResult('formResult', `表單提交成功！\n回應長度: ${text.length} 字元\n前 200 字元: ${text.substring(0, 200)}...`, 'success');
                } else {
                    testResults.form = false;
                    showResult('formResult', `表單提交失敗: ${response.status}\n${text}`, 'error');
                }
            } catch (error) {
                testResults.form = false;
                showResult('formResult', `表單提交錯誤: ${error.message}`, 'error');
            }
            
            updateTestSummary();
        }
        
        // 測試資料庫寫入
        async function testDatabaseWrite() {
            if (!config.gasUrl) {
                showResult('databaseResult', '請先設定 Google Apps Script URL', 'error');
                return;
            }
            
            const testData = new FormData();
            testData.append('action', 'add_record');
            testData.append('merchant_order_no', 'TEST' + Date.now());
            testData.append('amount', '1000');
            testData.append('donor_name', '測試寫入用戶');
            testData.append('donor_email', '<EMAIL>');
            testData.append('status', 'Pending');
            
            try {
                showResult('databaseResult', '正在測試資料庫寫入...', 'info');
                
                const response = await fetch(config.gasUrl, {
                    method: 'POST',
                    body: testData
                });
                
                const text = await response.text();
                
                if (response.ok) {
                    testResults.database = true;
                    showResult('databaseResult', `資料庫寫入成功！\n回應: ${text}`, 'success');
                } else {
                    testResults.database = false;
                    showResult('databaseResult', `資料庫寫入失敗: ${response.status}\n${text}`, 'error');
                }
            } catch (error) {
                testResults.database = false;
                showResult('databaseResult', `資料庫寫入錯誤: ${error.message}`, 'error');
            }
            
            updateTestSummary();
        }
        
        // 執行完整測試
        async function runFullTest() {
            showResult('fullTestResult', '開始執行完整測試...\n', 'info');
            
            const tests = [
                { name: '連接測試', func: testConnection },
                { name: '表單提交測試', func: testFormSubmission },
                { name: '資料庫寫入測試', func: testDatabaseWrite }
            ];
            
            for (const test of tests) {
                showResult('fullTestResult', `正在執行: ${test.name}\n`, 'info', true);
                await test.func();
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待 1 秒
            }
            
            showResult('fullTestResult', '完整測試執行完畢！請檢查各項測試結果。', 'success', true);
            testResults.fullTest = true;
            updateTestSummary();
        }
        
        // 顯示結果
        function showResult(elementId, message, type, append = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            
            if (append) {
                element.textContent += message;
            } else {
                element.textContent = message;
            }
        }
        
        // 更新測試總覽
        function updateTestSummary() {
            const summary = document.getElementById('testSummary');
            let html = '<h4>測試狀態：</h4>';
            
            const tests = [
                { name: '連接測試', result: testResults.connection },
                { name: '表單測試', result: testResults.form },
                { name: '資料庫測試', result: testResults.database },
                { name: '完整測試', result: testResults.fullTest }
            ];
            
            tests.forEach(test => {
                let status = 'pending';
                let statusText = '未測試';
                
                if (test.result === true) {
                    status = 'success';
                    statusText = '通過';
                } else if (test.result === false) {
                    status = 'error';
                    statusText = '失敗';
                }
                
                html += `<p>${test.name}: <span class="status ${status}">${statusText}</span></p>`;
            });
            
            summary.innerHTML = html;
        }
        
        // 清除所有結果
        function clearAllResults() {
            const resultElements = document.querySelectorAll('.result');
            resultElements.forEach(element => {
                element.style.display = 'none';
                element.textContent = '';
            });
            
            testResults = {
                connection: null,
                form: null,
                database: null,
                payment: null,
                fullTest: null
            };
            
            updateTestSummary();
        }
        
        // 頁面載入時自動載入設定
        window.onload = function() {
            loadConfig();
            updateTestSummary();
        };
    </script>
</body>
</html>
