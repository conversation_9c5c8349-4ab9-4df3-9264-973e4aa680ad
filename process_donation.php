<?php
// process_donation.php - <PERSON>les donation form submission and initiates NewebPay request

// --- NewebPay API Configuration ---
// Replace with your actual NewebPay keys and Merchant ID
$key = "vTxBcUTfd54kxzh01qborPcL5kJtMoxE";
$iv = "PEEDYyAVozYO6rDC";
$mid = "MS3757703701";
// --- End NewebPay API Configuration ---

// --- Database Connection Details ---
// Replace with your database connection details
$servername = "sql301.infinityfree.com";
$username = "if0_37792451";
$password = "tcbdct12";
$dbname = "if0_37792451_donate";
// --- End Database Connection Details ---

// Create database connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check database connection
if ($conn->connect_error) {
    // Log the database connection error instead of just dying
    $logFile = 'db_error_log.txt';
    $logMessage = "Database Connection failed at: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "Error: " . $conn->connect_error . "\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    // Display an error message to the user or redirect to an error page
    die("系統錯誤，請稍後再試。Database connection error logged.");
}

// Ensure the request is a POST request
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // --- Get data from the submitted form ---
    $donor_name = $_POST["donor_name"] ?? ''; // Use null coalescing operator for safety
    $donor_email = $_POST["donor_email"] ?? '';
    $payment_type_preference = $_POST["payment_type"] ?? ''; // User's preferred payment type
    $receipt_option = $_POST["receipt_option"] ?? ''; // User's receipt option

    // Calculate total amount from item amounts submitted
    $amount = 0;
    // Get item amounts, ensuring they are numbers and defaulting to 0
    $amount_general = isset($_POST["item_amount_general_expenses"]) ? intval($_POST["item_amount_general_expenses"]) : 0;
    $amount_book = isset($_POST["item_amount_book_printing"]) ? intval($_POST["item_amount_book_printing"]) : 0;
    $amount_life = isset($_POST["item_amount_life_release"]) ? intval($_POST["item_amount_life_release"]) : 0;
    $amount_clinic = isset($_POST["item_amount_free_clinic"]) ? intval($_POST["item_amount_free_clinic"]) : 0;
    $amount_building = isset($_POST["item_amount_building_repair"]) ? intval($_POST["item_amount_building_repair"]) : 0;
    $amount_jewels = isset($_POST["item_amount_offering_three_jewels"]) ? intval($_POST["item_amount_offering_three_jewels"]) : 0;

    $amount = $amount_general + $amount_book + $amount_life + $amount_clinic + $amount_building + $amount_jewels;

    // Ensure amount is greater than 0 (NewebPay requires this for most methods)
    if ($amount <= 0) {
         // Handle the case where the donation amount is zero or negative
        die("捐款金額必須大於零。");
    }


    // Generate unique Merchant Order Number
    // Using a prefix and current timestamp is common, but ensure true uniqueness if possible (e.g., add a random suffix or counter)
    $merchantOrderNo = "CDL" . time() . mt_rand(100, 999); // Added random suffix for better uniqueness

    $itemDesc = '慈光圖書館捐款'; // Item description for NewebPay

    // Define NotifyURL and ReturnURL
    // *** Crucially, ensure these URLs are correct and publicly accessible ***
    $notifyURL = 'https://amtbtc.infy.uk/donate/notify_url.php';
    $returnURL = 'https://amtbtc.infy.uk/donate/return_url.php';
    // ClientBackURL is optional, redirects user back to store from payment complete page
    // $clientBackURL = 'https://amtbtc.infy.uk/donate/donate.html'; // Or your donation form page URL

    // --- Prepare data array for NewebPay MPG request ---
    // This array matches the parameters required by NewebPay's MPG API (Section 4.2.1 of the manual)
    $data = array(
        'MerchantID' => $mid,
        'RespondType' => 'String', // Or 'JSON', but 'String' is common for form post redirect
        'TimeStamp' => time(),
        'Version' => '2.0', // Current recommended version
        'MerchantOrderNo' => $merchantOrderNo,
        'Amt' => $amount,
        'ItemDesc' => $itemDesc,
        'NotifyURL' => $notifyURL,
        'ReturnURL' => $returnURL,
        // 'ClientBackURL' => $clientBackURL, // Uncomment if you want a "Back to Store" button
        'Email' => $donor_email, // Pass donor email for notification
        'EmailModify' => 1, // Allow user to modify email on payment page (1=yes, 0=no)
        'LoginType' => 0, // Usually 0 for non-member checkout
        // --- Payment Method Flags ---
        // You need to enable the payment methods you want to appear on the MPG page.
        // Set the value to 1 to enable the method.
        // Based on your form, you have options for CREDIT, UNIONPAY, CVS, TAIWANPAY, LINEPAY, GOOGLEPAY, WEBATM, VACC, FOREIGN.
        // It's common to enable all relevant ones here, regardless of user's preference in the form,
        // as the user makes the final selection on the MPG page.
        'CREDIT' => 1, // Enable Credit Card
        'UNIONPAY' => 1, // Enable Union Pay
        'CVS' => 1, // Enable Convenience Store Code
        'TAIWANPAY' => 1, // Enable Taiwan Pay
        'LINEPAY' => 1, // Enable LINE Pay
        'GOOGLEPAY' => 1, // Enable Google Pay (Requires Google Pay setup in NewebPay)
        'WEBATM' => 1, // Enable WebATM
        'VACC' => 1, // Enable ATM Transfer
        'FOREIGN' => 1, // Enable Foreign Cards (Often handled by CREDIT flag depending on setup)
        // If you only want to enable the user's preferred method:
        /*
        'CREDIT' => ($payment_type_preference == 'CREDIT') ? 1 : 0,
        'UNIONPAY' => ($payment_type_preference == 'UNIONPAY') ? 1 : 0,
        'CVS' => ($payment_type_preference == 'CVS') ? 1 : 0,
        // ... and so on for other payment types
        */
        // You can add other optional parameters based on NewebPay manual Section 4.2.1 (e.g., LgsType for convenience store logistics)
    );

    // --- Build query string for encryption ---
    $data_query_string = http_build_query($data);

    // --- AES 256 Encryption (Step 2 in manual 4.1.1) ---
    // Use raw data output with PKCS7 padding implicitly handled by openssl_encrypt
    $tradeInfo = bin2hex(openssl_encrypt($data_query_string, "AES-256-CBC", $key, OPENSSL_RAW_DATA, $iv));

    // --- SHA 256 Hashing (Step 3 in manual 4.1.2) ---
    $tradeSha = strtoupper(hash("sha256", "HashKey=" . $key . "&" . $tradeInfo . "&HashIV=" . $iv));

    // --- Log the data being sent to NewebPay (for debugging) ---
    $logDataFile = 'newebpay_data_log.txt';
    $logMessage = "Data to Newebpay at: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "MerchantOrderNo: " . $merchantOrderNo . "\n";
    $logMessage .= "Amount: " . $amount . "\n";
    $logMessage .= "NotifyURL: " . $notifyURL . "\n";
    $logMessage .= "ReturnURL: " . $returnURL . "\n";
    $logMessage .= "Original Data String: " . $data_query_string . "\n";
    $logMessage .= "TradeInfo (Encrypted): " . $tradeInfo . "\n";
    $logMessage .= "TradeSha (Hashed): " . $tradeSha . "\n";
    $logMessage .= "----------------------------------------\n";
    file_put_contents($logDataFile, $logMessage, FILE_APPEND | LOCK_EX);

    // --- Store donation record in database (before redirecting) ---
   // You might want to store more details here, e.g., item breakdowns, receipt options
    // The item_id_value was null in your original code's bind_param.
    // If your table requires a non-null item_id, you'll need to adjust this or your schema.
    // Assuming 'merchant_order_no' is your primary key or unique identifier for this table.
    // MODIFIED: Changed 'payment_type_preference' column name to 'payment_type' to match potential existing DB schema
    $sql = "INSERT INTO donation_records (merchant_order_no, amount, donor_name, donor_email, payment_type, receipt_option, trade_info_sent, trade_sha_sent, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'Pending')"; // Added status column
    $stmt = $conn->prepare($sql);

    // You need to adjust the types in bind_param based on your table schema
    // Assuming merchant_order_no VARCHAR, amount INT, name VARCHAR, email VARCHAR, preference VARCHAR, receipt VARCHAR, info TEXT, sha TEXT, status VARCHAR
    $bind_types = "sdsssssss"; // s=string, d=double (for amount if needed, int is usually fine), i=integer

    $stmt->bind_param(
        $bind_types,
        $merchantOrderNo,
        $amount,
        $donor_name,
        $donor_email,
        $payment_type_preference,
        $receipt_option,
        $tradeInfo, // Store the data sent for debugging/verification later
        $tradeSha,  // Store the data sent
        $status = 'Pending' // Initial status
    );

    if (!$stmt->execute()) {
        // Log database insertion error
        $logFile = 'db_error_log.txt';
        $logMessage = "Database Insertion failed at: " . date('Y-m-d H:i:s') . "\n";
        $logMessage .= "MerchantOrderNo: " . $merchantOrderNo . "\n";
        $logMessage .= "Error: " . $stmt->error . "\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
        // Inform user about database error
        die("處理您的捐款時發生內部錯誤，請聯繫網站管理員。Database error logged.");
    }

    $stmt->close();
    $conn->close(); // Close DB connection after storing record

    // --- Auto-submit form to NewebPay MPG ---
    // This part generates an HTML form and automatically submits it via JavaScript
?>
<!DOCTYPE html>
<html>
<head>
    <title>導向支付頁面中...</title>
    <meta charset="utf-8">
</head>
<body>
    <p>正在安全導向支付頁面，請稍候...</p>

    <form id="newebpay-form" method="post" action="https://core.newebpay.com/MPG/mpg_gateway">
        <input type="hidden" name="MerchantID" value="<?php echo htmlspecialchars($mid); ?>">
        <input type="hidden" name="Version" value="<?php echo htmlspecialchars($data['Version']); ?>">
        <input type="hidden" name="TradeInfo" value="<?php echo htmlspecialchars($tradeInfo); ?>">
        <input type="hidden" name="TradeSha" value="<?php echo htmlspecialchars($tradeSha); ?>">
        <!-- 您可以根據需要添加其他非加密參數的隱藏欄位 -->
        <!-- 例如: <input type="hidden" name="RespondType" value="<?php echo htmlspecialchars($data['RespondType']); ?>"> -->
    </form>

    <script>
        document.getElementById("newebpay-form").submit();
    </script>
</body>
</html>

<?php

} else {
    // If accessed directly without POST data, you might redirect them to the form page
    header("Location: donate.html"); // Or donate.php if it's still rendering the form
    exit();
}

?>