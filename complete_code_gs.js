/**
 * 完整捐款系統 - Code.gs
 * 慈光圖書館線上捐款系統
 */

// === 系統設定 ===
const DONATION_CONFIG = {
  SPREADSHEET_ID: '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc',
  SHEET_NAME: 'donation_records',
  LOG_SHEET_NAME: 'system_logs',
  
  // 藍新金流設定
  NEWEBPAY: {
    KEY: 'vTxBcUTfd54kxzh01qborPcL5kJtMoxE',
    IV: 'PEEDYyAVozYO6rDC',
    MERCHANT_ID: 'MS3757703701',
    VERSION: '2.0',
    GATEWAY_URL: 'https://core.newebpay.com/MPG/mpg_gateway'
  }
};

/**
 * 處理 GET 請求
 */
function doGet(e) {
  try {
    const action = e.parameter.action || 'form';
    
    logOperation('GET Request', { action: action, parameters: e.parameters });
    
    switch (action) {
      case 'form':
        return showDonationForm();
      case 'test':
        return handleSystemTest();
      case 'return':
        return handlePaymentReturn(e);
      default:
        return showDonationForm();
    }
    
  } catch (error) {
    logOperation('GET Error', error.toString());
    return createErrorPage('系統錯誤，請稍後再試: ' + error.toString());
  }
}

/**
 * 處理 POST 請求
 */
function doPost(e) {
  try {
    const action = e.parameter.action || 'donate';
    
    logOperation('POST Request', { action: action });
    
    switch (action) {
      case 'donate':
        return handleDonationSubmission(e);
      case 'notify':
        return handlePaymentNotification(e);
      default:
        return createErrorPage('未知的操作');
    }
    
  } catch (error) {
    logOperation('POST Error', error.toString());
    return createErrorPage('處理請求時發生錯誤: ' + error.toString());
  }
}

/**
 * 顯示捐款表單
 */
function showDonationForm() {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>慈光圖書館 - 線上捐款</title>
      <style>
        body { font-family: 'Microsoft JhengHei', Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; font-size: 2.5em; }
        .header p { color: #7f8c8d; font-size: 1.1em; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: bold; color: #34495e; }
        .form-group input, .form-group select { width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; transition: border-color 0.3s; box-sizing: border-box; }
        .form-group input:focus, .form-group select:focus { border-color: #3498db; outline: none; }
        .donation-items { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 25px; border-radius: 10px; margin: 25px 0; }
        .donation-items h3 { color: #2c3e50; margin-bottom: 20px; text-align: center; }
        .donation-item { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 10px; background: white; border-radius: 8px; }
        .donation-item label { margin-bottom: 0; flex: 1; font-weight: 500; }
        .donation-item input { width: 150px; margin-left: 15px; }
        .total-amount { background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; font-size: 24px; font-weight: bold; margin: 20px 0; }
        .submit-btn { background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 18px 40px; border: none; border-radius: 25px; font-size: 20px; font-weight: bold; cursor: pointer; width: 100%; transition: transform 0.3s; }
        .submit-btn:hover { transform: translateY(-2px); }
        .submit-btn:disabled { background: #95a5a6; transform: none; cursor: not-allowed; }
        .required { color: #e74c3c; }
        .info-box { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .error { color: #e74c3c; background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 5px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🏛️ 慈光圖書館</h1>
          <h2>線上捐款系統</h2>
          <p>感謝您的護持，您的每一分善款都將用於弘法利生</p>
        </div>
        
        <div id="errorMessage" class="error" style="display: none;"></div>
        
        <form id="donationForm" method="post">
          <input type="hidden" name="action" value="donate">
          
          <div class="form-group">
            <label for="donor_name">捐款人姓名 <span class="required">*</span></label>
            <input type="text" id="donor_name" name="donor_name" required placeholder="請輸入您的姓名">
          </div>
          
          <div class="form-group">
            <label for="donor_email">電子郵件 <span class="required">*</span></label>
            <input type="email" id="donor_email" name="donor_email" required placeholder="<EMAIL>">
          </div>
          
          <div class="form-group">
            <label for="donor_phone">聯絡電話</label>
            <input type="tel" id="donor_phone" name="donor_phone" placeholder="09xxxxxxxx">
          </div>
          
          <div class="donation-items">
            <h3>🙏 護持項目</h3>
            <div class="donation-item">
              <label for="item_general">一般經費</label>
              <input type="number" id="item_general" name="item_amount_general_expenses" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_book">助印弘法</label>
              <input type="number" id="item_book" name="item_amount_book_printing" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_life">護生關懷助念</label>
              <input type="number" id="item_life" name="item_amount_life_release" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_clinic">義診慈善救濟</label>
              <input type="number" id="item_clinic" name="item_amount_free_clinic" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_building">建築修繕設備</label>
              <input type="number" id="item_building" name="item_amount_building_repair" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_jewels">供養三寶</label>
              <input type="number" id="item_jewels" name="item_amount_offering_three_jewels" min="0" value="0" onchange="calculateTotal()">
            </div>
          </div>
          
          <div class="total-amount">
            💰 總捐款金額：NT$ <span id="totalAmount">0</span>
          </div>
          
          <div class="form-group">
            <label for="receipt_option">收據選項</label>
            <select id="receipt_option" name="receipt_option">
              <option value="no_receipt">不需要收據</option>
              <option value="monthly_receipt">依次寄發收據</option>
              <option value="annual_receipt">年度收據</option>
            </select>
          </div>
          
          <div class="info-box">
            <p><strong>📋 付款說明：</strong></p>
            <ul>
              <li>支援信用卡、銀聯卡、超商條碼、LINE Pay、Google Pay 等多種付款方式</li>
              <li>所有交易均透過藍新金流安全加密處理</li>
              <li>捐款收據將依您的選擇寄發</li>
            </ul>
          </div>
          
          <button type="submit" class="submit-btn" id="submitBtn">🙏 確認捐款</button>
        </form>
      </div>
      
      <script>
        function calculateTotal() {
          const items = [
            'item_amount_general_expenses',
            'item_amount_book_printing', 
            'item_amount_life_release',
            'item_amount_free_clinic',
            'item_amount_building_repair',
            'item_amount_offering_three_jewels'
          ];
          
          let total = 0;
          items.forEach(item => {
            const value = parseInt(document.getElementsByName(item)[0].value) || 0;
            total += value;
          });
          
          document.getElementById('totalAmount').textContent = total.toLocaleString();
        }
        
        function showError(message) {
          const errorDiv = document.getElementById('errorMessage');
          errorDiv.textContent = message;
          errorDiv.style.display = 'block';
          errorDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function hideError() {
          document.getElementById('errorMessage').style.display = 'none';
        }
        
        document.getElementById('donationForm').addEventListener('submit', function(e) {
          hideError();
          
          // 驗證必要欄位
          const donorName = document.getElementById('donor_name').value.trim();
          const donorEmail = document.getElementById('donor_email').value.trim();
          
          if (!donorName) {
            e.preventDefault();
            showError('請輸入捐款人姓名');
            return false;
          }
          
          if (!donorEmail) {
            e.preventDefault();
            showError('請輸入電子郵件');
            return false;
          }
          
          // 驗證金額
          const total = parseInt(document.getElementById('totalAmount').textContent.replace(/,/g, ''));
          if (total <= 0) {
            e.preventDefault();
            showError('請輸入捐款金額');
            return false;
          }
          
          // 顯示處理中訊息
          const submitBtn = document.getElementById('submitBtn');
          submitBtn.innerHTML = '⏳ 處理中，請稍候...';
          submitBtn.disabled = true;
          
          // 允許表單提交
          return true;
        });
        
        // 初始計算
        calculateTotal();
      </script>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html)
    .setTitle('慈光圖書館線上捐款')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * 處理捐款表單提交
 */
function handleDonationSubmission(e) {
  try {
    logOperation('Donation Submission Started', 'Processing form data');
    
    // 解析表單資料
    const formData = {};
    Object.keys(e.parameters).forEach(key => {
      formData[key] = e.parameters[key][0];
    });
    
    logOperation('Form Data Received', formData);
    
    // 驗證必要欄位
    if (!formData.donor_name || !formData.donor_email) {
      return createErrorPage('請填寫必要資訊（姓名和電子郵件）');
    }
    
    // 計算總金額
    const totalAmount = calculateDonationAmount(formData);
    if (totalAmount <= 0) {
      return createErrorPage('捐款金額必須大於零');
    }
    
    // 生成訂單編號
    const merchantOrderNo = 'CDL' + Date.now() + Math.floor(Math.random() * 1000);
    
    logOperation('Order Generated', { 
      merchantOrderNo: merchantOrderNo, 
      amount: totalAmount 
    });
    
    // 儲存到 Google Sheets
    const donationRecord = createDonationRecord(formData, merchantOrderNo, totalAmount);
    saveDonationRecord(donationRecord);
    
    // 準備藍新金流資料
    const paymentData = prepareNewebPayData(formData, merchantOrderNo, totalAmount);
    const encryptedData = encryptPaymentData(paymentData);
    
    logOperation('Payment Data Prepared', { 
      merchantOrderNo: merchantOrderNo,
      encrypted: true 
    });
    
    // 返回付款表單
    return createPaymentForm(encryptedData);
    
  } catch (error) {
    logOperation('Donation Submission Error', error.toString());
    return createErrorPage('處理捐款時發生錯誤: ' + error.toString());
  }
}

/**
 * 計算捐款總金額
 */
function calculateDonationAmount(formData) {
  const amounts = [
    'item_amount_general_expenses',
    'item_amount_book_printing',
    'item_amount_life_release',
    'item_amount_free_clinic',
    'item_amount_building_repair',
    'item_amount_offering_three_jewels'
  ];

  let total = 0;
  amounts.forEach(field => {
    const amount = parseInt(formData[field]) || 0;
    total += amount;
  });

  return total;
}

/**
 * 建立捐款記錄
 */
function createDonationRecord(formData, merchantOrderNo, amount) {
  return [
    new Date().toLocaleString('zh-TW'),
    merchantOrderNo,
    amount,
    formData.donor_name || '',
    formData.donor_email || '',
    formData.donor_phone || '',
    '',  // payment_type
    formData.receipt_option || '',
    parseInt(formData.item_amount_general_expenses) || 0,
    parseInt(formData.item_amount_book_printing) || 0,
    parseInt(formData.item_amount_life_release) || 0,
    parseInt(formData.item_amount_free_clinic) || 0,
    parseInt(formData.item_amount_building_repair) || 0,
    parseInt(formData.item_amount_offering_three_jewels) || 0,
    'Pending',
    '',  // trade_info_sent
    '',  // trade_sha_sent
    '',  // newebpay_response
    new Date().toLocaleString('zh-TW'),
    ''   // notes
  ];
}

/**
 * 儲存捐款記錄
 */
function saveDonationRecord(record) {
  try {
    const spreadsheet = SpreadsheetApp.openById(DONATION_CONFIG.SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(DONATION_CONFIG.SHEET_NAME);
    sheet.appendRow(record);

    logOperation('Record Saved', {
      merchant_order_no: record[1],
      amount: record[2]
    });

  } catch (error) {
    logOperation('Save Record Error', error.toString());
  }
}

/**
 * 準備藍新金流資料
 */
function prepareNewebPayData(formData, merchantOrderNo, amount) {
  const currentUrl = ScriptApp.getService().getUrl();

  return {
    'MerchantID': DONATION_CONFIG.NEWEBPAY.MERCHANT_ID,
    'RespondType': 'String',
    'TimeStamp': Math.floor(Date.now() / 1000),
    'Version': DONATION_CONFIG.NEWEBPAY.VERSION,
    'MerchantOrderNo': merchantOrderNo,
    'Amt': amount,
    'ItemDesc': '慈光圖書館捐款',
    'NotifyURL': currentUrl + '?action=notify',
    'ReturnURL': currentUrl + '?action=return',
    'Email': formData.donor_email,
    'EmailModify': 1,
    'LoginType': 0,
    'CREDIT': 1,
    'UNIONPAY': 1,
    'CVS': 1,
    'TAIWANPAY': 1,
    'LINEPAY': 1,
    'GOOGLEPAY': 1,
    'WEBATM': 1,
    'VACC': 1,
    'FOREIGN': 1
  };
}

/**
 * 加密付款資料
 */
function encryptPaymentData(data) {
  // 建立查詢字串
  const queryString = Object.keys(data)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
    .join('&');

  // 簡化版加密 - 實際部署時需要真正的 AES 加密
  // 這裡使用 Base64 編碼作為示例
  const tradeInfo = Utilities.base64Encode(queryString);

  // 計算 SHA-256 雜湊
  const hashString = `HashKey=${DONATION_CONFIG.NEWEBPAY.KEY}&${tradeInfo}&HashIV=${DONATION_CONFIG.NEWEBPAY.IV}`;
  const tradeSha = Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, hashString)
    .map(byte => (byte < 0 ? byte + 256 : byte).toString(16).padStart(2, '0'))
    .join('').toUpperCase();

  return {
    tradeInfo: tradeInfo,
    tradeSha: tradeSha,
    merchantId: DONATION_CONFIG.NEWEBPAY.MERCHANT_ID,
    version: DONATION_CONFIG.NEWEBPAY.VERSION,
    originalData: queryString
  };
}

/**
 * 建立付款表單
 */
function createPaymentForm(encryptedData) {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <title>導向支付頁面</title>
      <meta charset="utf-8">
      <style>
        body {
          font-family: 'Microsoft JhengHei', Arial, sans-serif;
          text-align: center;
          padding: 50px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          margin: 0;
        }
        .container {
          background: white;
          padding: 40px;
          border-radius: 15px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          max-width: 500px;
          margin: 0 auto;
        }
        .loading {
          font-size: 20px;
          color: #2c3e50;
          margin: 20px 0;
        }
        .spinner {
          border: 4px solid #f3f3f3;
          border-top: 4px solid #3498db;
          border-radius: 50%;
          width: 50px;
          height: 50px;
          animation: spin 1s linear infinite;
          margin: 20px auto;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .info {
          background: #d1ecf1;
          border: 1px solid #bee5eb;
          color: #0c5460;
          padding: 15px;
          border-radius: 8px;
          margin: 20px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h2>🏛️ 慈光圖書館</h2>
        <div class="spinner"></div>
        <p class="loading">正在安全導向支付頁面，請稍候...</p>

        <div class="info">
          <p><strong>📋 注意事項：</strong></p>
          <ul style="text-align: left;">
            <li>請勿關閉瀏覽器或重新整理頁面</li>
            <li>系統將自動導向藍新金流安全付款頁面</li>
            <li>支援多種付款方式供您選擇</li>
          </ul>
        </div>

        <form id="payment-form" method="post" action="${DONATION_CONFIG.NEWEBPAY.GATEWAY_URL}">
          <input type="hidden" name="MerchantID" value="${encryptedData.merchantId}">
          <input type="hidden" name="Version" value="${encryptedData.version}">
          <input type="hidden" name="TradeInfo" value="${encryptedData.tradeInfo}">
          <input type="hidden" name="TradeSha" value="${encryptedData.tradeSha}">
        </form>

        <script>
          // 3秒後自動提交表單
          setTimeout(function() {
            document.getElementById("payment-form").submit();
          }, 3000);

          // 顯示倒數計時
          let countdown = 3;
          const countdownInterval = setInterval(function() {
            countdown--;
            if (countdown > 0) {
              document.querySelector('.loading').textContent =
                \`正在安全導向支付頁面，請稍候... (\${countdown}秒)\`;
            } else {
              clearInterval(countdownInterval);
            }
          }, 1000);
        </script>
      </div>
    </body>
    </html>
  `;

  return HtmlService.createHtmlOutput(html)
    .setTitle('導向支付頁面')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * 處理付款通知
 */
function handlePaymentNotification(e) {
  try {
    logOperation('Payment Notification Received', e.parameters);

    // 簡化版通知處理
    // 實際部署時需要解密和驗證資料

    return ContentService.createTextOutput('OK');

  } catch (error) {
    logOperation('Payment Notification Error', error.toString());
    return ContentService.createTextOutput('ERROR');
  }
}

/**
 * 處理付款返回
 */
function handlePaymentReturn(e) {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <title>付款結果</title>
      <meta charset="utf-8">
      <style>
        body {
          font-family: 'Microsoft JhengHei', Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background: white;
          padding: 40px;
          border-radius: 15px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
        }
        .success {
          color: #27ae60;
          font-size: 24px;
          margin: 20px 0;
        }
        .info {
          background: #d1ecf1;
          border: 1px solid #bee5eb;
          color: #0c5460;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
        }
        .button {
          display: inline-block;
          background: #3498db;
          color: white;
          padding: 12px 24px;
          text-decoration: none;
          border-radius: 8px;
          margin: 10px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🏛️ 慈光圖書館</h1>
        <div class="success">
          <h2>✅ 付款處理完成</h2>
        </div>

        <div class="info">
          <h3>📋 處理結果</h3>
          <p>感謝您的護持！我們已收到您的付款資訊。</p>
          <p>系統正在處理您的捐款，請稍候片刻。</p>
          <p>如有任何問題，請聯繫我們。</p>
        </div>

        <div class="info">
          <h3>📞 聯絡資訊</h3>
          <p>電話：(02) 1234-5678</p>
          <p>信箱：<EMAIL></p>
        </div>

        <a href="?" class="button">返回捐款頁面</a>
      </div>
    </body>
    </html>
  `;

  return HtmlService.createHtmlOutput(html)
    .setTitle('付款結果')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * 記錄系統操作
 */
function logOperation(type, data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(DONATION_CONFIG.SPREADSHEET_ID);
    let logSheet = spreadsheet.getSheetByName(DONATION_CONFIG.LOG_SHEET_NAME);

    logSheet.appendRow([
      new Date(),
      type,
      typeof data === 'object' ? JSON.stringify(data) : data,
      Session.getActiveUser().getEmail()
    ]);

  } catch (error) {
    console.error('Logging failed:', error);
  }
}

/**
 * 系統測試
 */
function handleSystemTest() {
  try {
    const spreadsheet = SpreadsheetApp.openById(DONATION_CONFIG.SPREADSHEET_ID);

    const result = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      spreadsheet_access: true,
      spreadsheet_name: spreadsheet.getName(),
      sheets_count: spreadsheet.getSheets().length,
      sheet_names: spreadsheet.getSheets().map(sheet => sheet.getName()),
      message: '完整系統測試成功'
    };

    return ContentService
      .createTextOutput(JSON.stringify(result, null, 2))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    const errorResult = {
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.toString()
    };

    return ContentService
      .createTextOutput(JSON.stringify(errorResult, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 建立錯誤頁面
 */
function createErrorPage(message) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>錯誤</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f8f9fa; }
        .error-container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error { color: #dc3545; font-size: 18px; margin: 20px 0; }
        .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
      </style>
    </head>
    <body>
      <div class="error-container">
        <h2>⚠️ 系統提示</h2>
        <p class="error">${message}</p>
        <a href="?" class="button">返回捐款頁面</a>
      </div>
    </body>
    </html>
  `;

  return HtmlService.createHtmlOutput(html);
}
