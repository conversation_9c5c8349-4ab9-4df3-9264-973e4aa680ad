<?php
// newebpay_official_test.php - 符合藍新金流官方技術手冊的完整測試

// === 藍新金流官方設定 ===
$NEWEBPAY_CONFIG = [
    'key' => 'vTxBcUTfd54kxzh01qborPcL5kJtMoxE',
    'iv' => 'PEEDYyAVozYO6rDC',
    'merchant_id' => 'MS3757703701',
    'version' => '2.0',
    'gateway_url' => 'https://core.newebpay.com/MPG/mpg_gateway'
];

echo "<h1>藍新金流官方技術手冊規範測試</h1>\n";
echo "<style>body{font-family:Arial,sans-serif;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>\n";

// === 測試 1: 加密功能測試 ===
echo "<h2>測試 1: 加密功能測試（符合官方規範）</h2>\n";

// 準備測試資料（符合藍新金流 MPG 規範）
$testOrderNo = 'TEST' . time();
$testData = [
    'MerchantID' => $NEWEBPAY_CONFIG['merchant_id'],
    'RespondType' => 'String',
    'TimeStamp' => time(),
    'Version' => $NEWEBPAY_CONFIG['version'],
    'MerchantOrderNo' => $testOrderNo,
    'Amt' => 1000,
    'ItemDesc' => '測試捐款',
    'NotifyURL' => 'https://script.google.com/macros/s/您的Google Apps Script ID/exec',
    'ReturnURL' => 'https://amtbtc.infy.uk/donate/return_url.php',
    'Email' => '<EMAIL>',
    'EmailModify' => 1,
    'LoginType' => 0,
    'CREDIT' => 1,
    'UNIONPAY' => 1,
    'CVS' => 1,
    'TAIWANPAY' => 1,
    'LINEPAY' => 1,
    'GOOGLEPAY' => 1,
    'WEBATM' => 1,
    'VACC' => 1,
    'FOREIGN' => 1
];

// 步驟 1: 建立查詢字串
$queryString = http_build_query($testData);
echo "<div class='info'>原始資料查詢字串:</div>\n";
echo "<pre>" . htmlspecialchars($queryString) . "</pre>\n";

// 步驟 2: AES-256-CBC 加密
$tradeInfo = bin2hex(openssl_encrypt($queryString, "AES-256-CBC", $NEWEBPAY_CONFIG['key'], OPENSSL_RAW_DATA, $NEWEBPAY_CONFIG['iv']));
echo "<div class='info'>加密後的 TradeInfo (Hex):</div>\n";
echo "<pre>" . htmlspecialchars($tradeInfo) . "</pre>\n";

// 步驟 3: SHA-256 雜湊
$tradeSha = strtoupper(hash("sha256", "HashKey=" . $NEWEBPAY_CONFIG['key'] . "&" . $tradeInfo . "&HashIV=" . $NEWEBPAY_CONFIG['iv']));
echo "<div class='info'>SHA-256 雜湊值 (TradeSha):</div>\n";
echo "<pre>" . htmlspecialchars($tradeSha) . "</pre>\n";

// === 測試 2: 解密功能測試 ===
echo "<h2>測試 2: 解密功能測試（驗證加密正確性）</h2>\n";

// 解密測試
$decryptedHex = hex2bin($tradeInfo);
$decryptedData = openssl_decrypt($decryptedHex, "AES-256-CBC", $NEWEBPAY_CONFIG['key'], OPENSSL_RAW_DATA, $NEWEBPAY_CONFIG['iv']);

if ($decryptedData !== false) {
    echo "<div class='success'>✅ 解密成功</div>\n";
    echo "<div class='info'>解密後的資料:</div>\n";
    echo "<pre>" . htmlspecialchars($decryptedData) . "</pre>\n";
    
    // 解析參數
    $decryptedParams = [];
    parse_str($decryptedData, $decryptedParams);
    
    echo "<div class='info'>解析後的參數:</div>\n";
    echo "<pre>" . print_r($decryptedParams, true) . "</pre>\n";
    
    // 驗證關鍵欄位
    $keyFields = ['MerchantOrderNo', 'Amt', 'MerchantID'];
    $allMatch = true;
    
    foreach ($keyFields as $field) {
        if (isset($decryptedParams[$field]) && $decryptedParams[$field] == $testData[$field]) {
            echo "<div class='success'>✅ {$field}: 匹配正確</div>\n";
        } else {
            echo "<div class='error'>❌ {$field}: 不匹配</div>\n";
            $allMatch = false;
        }
    }
    
    if ($allMatch) {
        echo "<div class='success'><strong>✅ 加密解密測試完全通過！</strong></div>\n";
    }
    
} else {
    echo "<div class='error'>❌ 解密失敗</div>\n";
}

// === 測試 3: SHA-256 驗證測試 ===
echo "<h2>測試 3: SHA-256 簽章驗證測試</h2>\n";

// 重新計算 SHA-256
$verifyHash = strtoupper(hash("sha256", "HashKey=" . $NEWEBPAY_CONFIG['key'] . "&" . $tradeInfo . "&HashIV=" . $NEWEBPAY_CONFIG['iv']));

if ($verifyHash === $tradeSha) {
    echo "<div class='success'>✅ SHA-256 簽章驗證通過</div>\n";
} else {
    echo "<div class='error'>❌ SHA-256 簽章驗證失敗</div>\n";
    echo "<div class='info'>原始: {$tradeSha}</div>\n";
    echo "<div class='info'>驗證: {$verifyHash}</div>\n";
}

// === 測試 4: 模擬藍新金流通知測試 ===
echo "<h2>測試 4: 模擬藍新金流通知測試</h2>\n";

// 模擬藍新金流回傳的通知資料
$notifyData = [
    'Status' => 'SUCCESS',
    'MerchantID' => $NEWEBPAY_CONFIG['merchant_id'],
    'Version' => $NEWEBPAY_CONFIG['version'],
    'TradeInfo' => $tradeInfo,
    'TradeSha' => $tradeSha,
    'MerchantOrderNo' => $testOrderNo,
    'Amt' => 1000,
    'TradeNo' => 'T' . time(),
    'PaymentType' => 'CREDIT',
    'RespondType' => 'String',
    'PayTime' => date('Y-m-d H:i:s'),
    'IP' => '127.0.0.1',
    'EscrowBank' => 'HNCB'
];

// 建立模擬通知的加密資料
$notifyQueryString = http_build_query($notifyData);
$notifyTradeInfo = bin2hex(openssl_encrypt($notifyQueryString, "AES-256-CBC", $NEWEBPAY_CONFIG['key'], OPENSSL_RAW_DATA, $NEWEBPAY_CONFIG['iv']));
$notifyTradeSha = strtoupper(hash("sha256", "HashKey=" . $NEWEBPAY_CONFIG['key'] . "&" . $notifyTradeInfo . "&HashIV=" . $NEWEBPAY_CONFIG['iv']));

echo "<div class='info'>模擬通知資料:</div>\n";
echo "<pre>TradeInfo: " . htmlspecialchars($notifyTradeInfo) . "\nTradeSha: " . htmlspecialchars($notifyTradeSha) . "</pre>\n";

// === 測試 5: 解密服務測試 ===
echo "<h2>測試 5: 解密服務測試</h2>\n";

$decryptServiceUrl = 'https://amtbtc.infy.uk/donate/decrypt_service.php';

// 測試解密服務
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $decryptServiceUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'tradeInfo' => $notifyTradeInfo,
    'key' => $NEWEBPAY_CONFIG['key'],
    'iv' => $NEWEBPAY_CONFIG['iv']
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($httpCode === 200 && !$error) {
    echo "<div class='success'>✅ 解密服務連接成功</div>\n";
    echo "<div class='info'>服務回應:</div>\n";
    echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
    
    $serviceResult = json_decode($response, true);
    if ($serviceResult && $serviceResult['success']) {
        echo "<div class='success'>✅ 解密服務功能正常</div>\n";
    } else {
        echo "<div class='error'>❌ 解密服務回傳錯誤</div>\n";
    }
} else {
    echo "<div class='error'>❌ 解密服務連接失敗: HTTP {$httpCode}, Error: {$error}</div>\n";
    echo "<div class='info'>請確認 decrypt_service.php 已正確上傳並可存取</div>\n";
}

// === 測試 6: Google Apps Script 通知測試 ===
echo "<h2>測試 6: Google Apps Script 通知端點測試</h2>\n";

$gasUrl = 'https://script.google.com/macros/s/您的Google Apps Script ID/exec';

echo "<div class='info'>測試 URL: {$gasUrl}</div>\n";
echo "<div class='info'>請將上方 URL 中的「您的Google Apps Script ID」替換為實際的 ID</div>\n";

// 如果 URL 已設定，進行測試
if (strpos($gasUrl, '您的Google Apps Script ID') === false) {
    $gasTestData = [
        'TradeInfo' => $notifyTradeInfo,
        'TradeSha' => $notifyTradeSha
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $gasUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($gasTestData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $gasResponse = curl_exec($ch);
    $gasHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $gasError = curl_error($ch);
    curl_close($ch);
    
    if ($gasHttpCode === 200 && !$gasError) {
        echo "<div class='success'>✅ Google Apps Script 連接成功</div>\n";
        echo "<div class='info'>回應:</div>\n";
        echo "<pre>" . htmlspecialchars($gasResponse) . "</pre>\n";
    } else {
        echo "<div class='error'>❌ Google Apps Script 連接失敗: HTTP {$gasHttpCode}, Error: {$gasError}</div>\n";
    }
} else {
    echo "<div class='info'>⚠️ 請先設定正確的 Google Apps Script URL</div>\n";
}

// === 總結 ===
echo "<h2>測試總結</h2>\n";
echo "<div class='info'>";
echo "<strong>重要提醒：</strong><br>\n";
echo "1. 請確認所有金鑰和 ID 都是正確的測試環境值<br>\n";
echo "2. 在正式環境中，請使用藍新金流提供的正式金鑰<br>\n";
echo "3. 確保所有 URL 都可以被藍新金流伺服器存取<br>\n";
echo "4. 建議先在藍新金流測試環境進行完整測試<br>\n";
echo "5. 所有加密解密都符合藍新金流官方技術手冊規範\n";
echo "</div>\n";

echo "<div class='success'><strong>如果以上測試都通過，您的系統已準備好與藍新金流整合！</strong></div>\n";
?>
