<?php
// test_notify.php - Test script to check if external requests can reach your server

// Set response headers
header('Content-Type: text/plain');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$logFile = 'test_notify_log.txt';
$logMessage = "=== Test Request Received ===\n";
$logMessage .= "Timestamp: " . date('Y-m-d H:i:s') . "\n";
$logMessage .= "Request Method: " . $_SERVER['REQUEST_METHOD'] . "\n";
$logMessage .= "Remote IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\n";
$logMessage .= "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";

// Log all headers
$logMessage .= "Headers:\n";
if (function_exists('getallheaders')) {
    foreach (getallheaders() as $name => $value) {
        $logMessage .= "  $name: $value\n";
    }
} else {
    $logMessage .= "  getallheaders() not available\n";
}

// Log POST data
if (!empty($_POST)) {
    $logMessage .= "POST Data: " . print_r($_POST, true) . "\n";
}

// Log GET data
if (!empty($_GET)) {
    $logMessage .= "GET Data: " . print_r($_GET, true) . "\n";
}

// Log raw input
$rawInput = file_get_contents('php://input');
if (!empty($rawInput)) {
    $logMessage .= "Raw Input: " . $rawInput . "\n";
}

$logMessage .= "=== End Test Request ===\n\n";

// Try to write to log file
$writeResult = file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

// Response
echo "Test Notify Endpoint\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";
echo "Request Method: " . $_SERVER['REQUEST_METHOD'] . "\n";
echo "Log Write Result: " . ($writeResult !== false ? 'Success' : 'Failed') . "\n";

if (!empty($_POST)) {
    echo "POST Data Received: Yes\n";
} else {
    echo "POST Data Received: No\n";
}

// Return OK for payment gateway testing
echo "Status: OK\n";
?>
