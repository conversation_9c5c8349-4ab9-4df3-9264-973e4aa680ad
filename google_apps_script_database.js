/**
 * Google Apps Script - 資料庫操作處理器
 * 這個腳本處理所有與 Google Sheets 資料庫相關的操作
 */

// === 設定區域 ===
const CONFIG = {
  SPREADSHEET_ID: '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc',
  SHEET_NAME: 'donation_records',
  LOG_SHEET_NAME: 'operation_logs'
};

/**
 * Web App 主要入口點
 */
function doPost(e) {
  try {
    const action = e.parameter.action || e.parameters.action?.[0];
    
    logOperation('POST Request', { action: action, parameters: e.parameters });
    
    switch (action) {
      case 'add_record':
        return handleAddRecord(e);
      case 'update_status':
        return handleUpdateStatus(e);
      case 'get_record':
        return handleGetRecord(e);
      default:
        return createJsonResponse({ success: false, error: 'Unknown action' });
    }
    
  } catch (error) {
    logOperation('Exception', error.toString());
    return createJsonResponse({ success: false, error: 'Internal server error' });
  }
}

/**
 * 處理 GET 請求
 */
function doGet(e) {
  try {
    const action = e.parameter.action;
    
    switch (action) {
      case 'get_record':
        return handleGetRecord(e);
      case 'test':
        return createJsonResponse({ 
          success: true, 
          message: 'Google Apps Script Database API is working',
          timestamp: new Date().toISOString()
        });
      default:
        return createJsonResponse({ success: false, error: 'Unknown action' });
    }
    
  } catch (error) {
    logOperation('GET Exception', error.toString());
    return createJsonResponse({ success: false, error: 'Internal server error' });
  }
}

/**
 * 處理新增記錄請求
 */
function handleAddRecord(e) {
  try {
    const params = e.parameters || e.parameter;
    
    // 建立記錄陣列 (對應 Google Sheets 的欄位順序)
    const record = [
      params.timestamp?.[0] || new Date().toLocaleString('zh-TW'),
      params.merchant_order_no?.[0] || '',
      parseInt(params.amount?.[0]) || 0,
      params.donor_name?.[0] || '',
      params.donor_email?.[0] || '',
      params.donor_phone?.[0] || '',
      params.payment_type?.[0] || '',
      params.receipt_option?.[0] || '',
      parseInt(params.item_general?.[0]) || 0,
      parseInt(params.item_book?.[0]) || 0,
      parseInt(params.item_life?.[0]) || 0,
      parseInt(params.item_clinic?.[0]) || 0,
      parseInt(params.item_building?.[0]) || 0,
      parseInt(params.item_jewels?.[0]) || 0,
      params.status?.[0] || 'Pending',
      params.trade_info_sent?.[0] || '',
      params.trade_sha_sent?.[0] || '',
      params.newebpay_response?.[0] || '',
      params.updated_at?.[0] || new Date().toLocaleString('zh-TW'),
      params.notes?.[0] || ''
    ];
    
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(CONFIG.SHEET_NAME);
    
    if (!sheet) {
      return createJsonResponse({ success: false, error: 'Sheet not found' });
    }
    
    // 新增記錄到工作表
    sheet.appendRow(record);
    
    logOperation('Record Added', { 
      merchant_order_no: record[1], 
      amount: record[2],
      donor_name: record[3]
    });
    
    return createJsonResponse({ 
      success: true, 
      message: 'Record added successfully',
      merchant_order_no: record[1]
    });
    
  } catch (error) {
    logOperation('Add Record Error', error.toString());
    return createJsonResponse({ success: false, error: error.toString() });
  }
}

/**
 * 處理更新狀態請求
 */
function handleUpdateStatus(e) {
  try {
    const params = e.parameters || e.parameter;
    const merchantOrderNo = params.merchant_order_no?.[0];
    const status = params.status?.[0];
    const responseData = params.newebpay_response?.[0] || '';
    const updatedAt = new Date().toLocaleString('zh-TW');
    
    if (!merchantOrderNo || !status) {
      return createJsonResponse({ success: false, error: 'Missing required parameters' });
    }
    
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(CONFIG.SHEET_NAME);
    
    if (!sheet) {
      return createJsonResponse({ success: false, error: 'Sheet not found' });
    }
    
    // 查找對應的記錄
    const dataRange = sheet.getDataRange();
    const values = dataRange.getValues();
    
    let rowIndex = -1;
    for (let i = 1; i < values.length; i++) { // 跳過標題列
      if (values[i][1] === merchantOrderNo) { // 訂單編號在第 2 欄 (索引 1)
        rowIndex = i + 1; // Google Sheets 行號從 1 開始
        break;
      }
    }
    
    if (rowIndex === -1) {
      return createJsonResponse({ success: false, error: 'Record not found' });
    }
    
    // 更新狀態相關欄位
    sheet.getRange(rowIndex, 15).setValue(status);        // O 欄：狀態
    sheet.getRange(rowIndex, 18).setValue(responseData);  // R 欄：藍新回應
    sheet.getRange(rowIndex, 19).setValue(updatedAt);     // S 欄：更新時間
    
    logOperation('Status Updated', { 
      merchant_order_no: merchantOrderNo, 
      status: status,
      row: rowIndex
    });
    
    return createJsonResponse({ 
      success: true, 
      message: 'Status updated successfully',
      merchant_order_no: merchantOrderNo,
      status: status
    });
    
  } catch (error) {
    logOperation('Update Status Error', error.toString());
    return createJsonResponse({ success: false, error: error.toString() });
  }
}

/**
 * 處理查詢記錄請求
 */
function handleGetRecord(e) {
  try {
    const params = e.parameters || e.parameter;
    const merchantOrderNo = params.merchant_order_no?.[0] || params.merchant_order_no;
    
    if (!merchantOrderNo) {
      return createJsonResponse({ success: false, error: 'Missing merchant_order_no' });
    }
    
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(CONFIG.SHEET_NAME);
    
    if (!sheet) {
      return createJsonResponse({ success: false, error: 'Sheet not found' });
    }
    
    // 查找記錄
    const dataRange = sheet.getDataRange();
    const values = dataRange.getValues();
    const headers = values[0]; // 第一列是標題
    
    for (let i = 1; i < values.length; i++) {
      if (values[i][1] === merchantOrderNo) { // 訂單編號在第 2 欄
        // 建立記錄物件
        const record = {};
        headers.forEach((header, index) => {
          record[header] = values[i][index];
        });
        
        return createJsonResponse({ 
          success: true, 
          data: record 
        });
      }
    }
    
    return createJsonResponse({ success: false, error: 'Record not found' });
    
  } catch (error) {
    logOperation('Get Record Error', error.toString());
    return createJsonResponse({ success: false, error: error.toString() });
  }
}

/**
 * 記錄操作日誌
 */
function logOperation(type, data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    let logSheet = spreadsheet.getSheetByName(CONFIG.LOG_SHEET_NAME);
    
    // 如果日誌工作表不存在，建立它
    if (!logSheet) {
      logSheet = spreadsheet.insertSheet(CONFIG.LOG_SHEET_NAME);
      logSheet.getRange(1, 1, 1, 4).setValues([
        ['Timestamp', 'Type', 'Data', 'User']
      ]);
    }
    
    // 添加日誌記錄
    logSheet.appendRow([
      new Date(),
      type,
      typeof data === 'object' ? JSON.stringify(data) : data,
      Session.getActiveUser().getEmail()
    ]);
    
  } catch (error) {
    console.error('Logging failed:', error);
  }
}

/**
 * 建立 JSON 回應
 */
function createJsonResponse(data) {
  return ContentService
    .createTextOutput(JSON.stringify(data))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * 初始化工作表結構
 */
function initializeSheets() {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    
    // 建立主要資料工作表
    let mainSheet = spreadsheet.getSheetByName(CONFIG.SHEET_NAME);
    if (!mainSheet) {
      mainSheet = spreadsheet.insertSheet(CONFIG.SHEET_NAME);
      
      // 設定標題列
      const headers = [
        'timestamp', 'merchant_order_no', 'amount', 'donor_name', 'donor_email',
        'donor_phone', 'payment_type', 'receipt_option', 'item_general', 'item_book',
        'item_life', 'item_clinic', 'item_building', 'item_jewels', 'status',
        'trade_info_sent', 'trade_sha_sent', 'newebpay_response', 'updated_at', 'notes'
      ];
      
      mainSheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      // 設定格式
      mainSheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
      mainSheet.setFrozenRows(1);
    }
    
    // 建立日誌工作表
    let logSheet = spreadsheet.getSheetByName(CONFIG.LOG_SHEET_NAME);
    if (!logSheet) {
      logSheet = spreadsheet.insertSheet(CONFIG.LOG_SHEET_NAME);
      logSheet.getRange(1, 1, 1, 4).setValues([
        ['Timestamp', 'Type', 'Data', 'User']
      ]);
      logSheet.getRange(1, 1, 1, 4).setFontWeight('bold');
      logSheet.setFrozenRows(1);
    }
    
    console.log('Sheets initialized successfully');
    
  } catch (error) {
    console.error('Sheet initialization failed:', error);
  }
}
