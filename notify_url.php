<?php
// notify_url.php - Receives background notification from NewebPay

// --- NewebPay API Configuration ---
// You will need these to verify the incoming data later
// $key = "vTxBcUTfd54kxzh01qborPcL5kJtMoxE";
// $iv = "PEEDYyAVozYO6rDC";
// --- End NewebPay API Configuration ---

// --- Database Connection Details ---
// You will need these to update the order status later
// $servername = "sql301.infinityfree.com";
// $username = "if0_37792451";
// $password = "tcbdct12";
// $dbname = "if0_37792451_donate";
// --- End Database Connection Details ---


// Log file for incoming Notify data
$logFile = 'notify_log.txt';
$logMessage = "Notify received at: " . date('Y-m-d H:i:s') . "\n";

// Get all POST data sent by NewebPay
// NewebPay usually sends TradeInfo and TradeSha as top-level POST parameters
$postData = $_POST;
$logMessage .= "POST Data: " . print_r($postData, true) . "\n";

// Log the message to file
// This is the crucial line that should create notify_log.txt if the script is executed
file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

// --- IMPORTANT: Verification and Database Update Logic GOES HERE ---
// You need to add code here to:
// 1. Check if TradeInfo and TradeSha are present in $postData
// 2. Verify the data using your $key and $iv (decrypt TradeInfo, re-hash and compare TradeSha) - See manual 4.1.4 & 4.1.5/4.1.6
// 3. Based on the verified Status (e.g., SUCCESS) and MerchantOrderNo, update the corresponding record in your database.
// 4. Handle potential duplicate notifications (use a flag in DB or check if status is already updated).
// 5. Respond with a simple "OK" or similar string, AND ensure HTTP status code is 200.


// For now, just confirming receipt in the log. A more robust implementation is needed.
// Echo a simple string. NewebPay expects a 200 OK response.
// Ensure no extra whitespace or output before this line.
echo 'OK'; // Or 'Notify URL received', as long as it's a 200 OK response

?>