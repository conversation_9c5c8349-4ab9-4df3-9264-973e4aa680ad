<!DOCTYPE html>
<html>
<head>
    <title>測試通知端點</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>測試通知端點</h1>
        <p>這個頁面可以幫助您測試 notify_url.php 是否能正常接收請求。</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="endpoint">測試端點:</label>
                <input type="text" id="endpoint" value="test_notify.php" placeholder="例如: test_notify.php">
            </div>
            
            <div class="form-group">
                <label for="testData">測試資料 (JSON格式):</label>
                <textarea id="testData" rows="8" placeholder='{"TradeInfo": "test_encrypted_data", "TradeSha": "test_hash", "MerchantOrderNo": "TEST123"}'>{
  "TradeInfo": "test_encrypted_data",
  "TradeSha": "test_hash_value",
  "MerchantOrderNo": "TEST" + Date.now(),
  "Status": "SUCCESS",
  "Message": "Test notification"
}</textarea>
            </div>
            
            <button type="button" onclick="testEndpoint()">發送測試請求</button>
            <button type="button" onclick="testActualNotify()">測試實際 notify_url.php</button>
        </form>
        
        <div id="result" class="result" style="display: none;">
            <h3>測試結果:</h3>
            <pre id="resultContent"></pre>
        </div>
    </div>

    <script>
        async function testEndpoint() {
            const endpoint = document.getElementById('endpoint').value;
            const testData = document.getElementById('testData').value;
            
            try {
                const data = JSON.parse(testData);
                const formData = new FormData();
                
                // Convert JSON to form data
                for (const key in data) {
                    formData.append(key, data[key]);
                }
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.text();
                showResult(`狀態碼: ${response.status}\n\n回應內容:\n${result}`);
                
            } catch (error) {
                showResult(`錯誤: ${error.message}`);
            }
        }
        
        async function testActualNotify() {
            document.getElementById('endpoint').value = 'notify_url.php';
            await testEndpoint();
        }
        
        function showResult(content) {
            document.getElementById('resultContent').textContent = content;
            document.getElementById('result').style.display = 'block';
        }
    </script>
</body>
</html>
