/**
 * 最小化測試程式碼 - Code.gs
 * 只包含最基本的功能，避免任何可能的衝突
 */

/**
 * 處理 GET 請求
 */
function doGet(e) {
  try {
    // 簡單的測試回應
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>慈光圖書館捐款系統測試</title>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
          .success { color: green; font-size: 24px; margin: 20px 0; }
          .info { background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .test-btn { background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px; display: inline-block; }
        </style>
      </head>
      <body>
        <h1>🏛️ 慈光圖書館捐款系統</h1>
        <div class="success">✅ Web App 運作正常！</div>
        
        <div class="info">
          <h3>系統狀態</h3>
          <p><strong>時間：</strong>${new Date().toLocaleString('zh-TW')}</p>
          <p><strong>狀態：</strong>基本功能測試成功</p>
          <p><strong>版本：</strong>最小化測試版</p>
        </div>
        
        <div>
          <a href="?action=test" class="test-btn">測試 Sheets 連接</a>
          <a href="?action=info" class="test-btn">系統資訊</a>
        </div>
        
        <div class="info">
          <h3>測試結果</h3>
          <p>如果您看到這個頁面，表示：</p>
          <ul style="text-align: left; display: inline-block;">
            <li>✅ Google Apps Script 部署成功</li>
            <li>✅ Web App URL 可以正常存取</li>
            <li>✅ HTML 渲染功能正常</li>
            <li>✅ 基本路由功能正常</li>
          </ul>
        </div>
      </body>
      </html>
    `;
    
    // 檢查動作參數
    const action = e.parameter.action || 'home';
    
    if (action === 'test') {
      return testSheetsConnection();
    } else if (action === 'info') {
      return showSystemInfo();
    } else {
      return HtmlService.createHtmlOutput(html);
    }
    
  } catch (error) {
    return HtmlService.createHtmlOutput(`
      <html>
        <body style="font-family: Arial; padding: 20px; text-align: center;">
          <h1 style="color: red;">錯誤</h1>
          <p>發生錯誤：${error.toString()}</p>
          <p>時間：${new Date().toLocaleString('zh-TW')}</p>
        </body>
      </html>
    `);
  }
}

/**
 * 處理 POST 請求
 */
function doPost(e) {
  return HtmlService.createHtmlOutput(`
    <html>
      <body style="font-family: Arial; padding: 20px; text-align: center;">
        <h1>POST 請求測試</h1>
        <p>✅ POST 請求已成功接收</p>
        <p>時間：${new Date().toLocaleString('zh-TW')}</p>
      </body>
    </html>
  `);
}

/**
 * 測試 Google Sheets 連接
 */
function testSheetsConnection() {
  try {
    const sheetsId = '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc';
    const spreadsheet = SpreadsheetApp.openById(sheetsId);
    const sheets = spreadsheet.getSheets();
    
    const html = `
      <html>
        <head>
          <title>Sheets 連接測試</title>
          <meta charset="utf-8">
          <style>
            body { font-family: Arial; padding: 20px; }
            .success { color: green; }
            .info { background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
          </style>
        </head>
        <body>
          <h1>📊 Google Sheets 連接測試</h1>
          <div class="success">
            <h2>✅ 連接成功！</h2>
          </div>
          
          <div class="info">
            <h3>試算表資訊</h3>
            <p><strong>名稱：</strong>${spreadsheet.getName()}</p>
            <p><strong>工作表數量：</strong>${sheets.length}</p>
            <p><strong>工作表列表：</strong></p>
            <ul>
              ${sheets.map(sheet => `<li>${sheet.getName()}</li>`).join('')}
            </ul>
          </div>
          
          <div class="info">
            <p><a href="?">返回首頁</a></p>
          </div>
        </body>
      </html>
    `;
    
    return HtmlService.createHtmlOutput(html);
    
  } catch (error) {
    const html = `
      <html>
        <head>
          <title>Sheets 連接錯誤</title>
          <meta charset="utf-8">
          <style>
            body { font-family: Arial; padding: 20px; }
            .error { color: red; }
          </style>
        </head>
        <body>
          <h1>📊 Google Sheets 連接測試</h1>
          <div class="error">
            <h2>❌ 連接失敗</h2>
            <p>錯誤：${error.toString()}</p>
          </div>
          <p><a href="?">返回首頁</a></p>
        </body>
      </html>
    `;
    
    return HtmlService.createHtmlOutput(html);
  }
}

/**
 * 顯示系統資訊
 */
function showSystemInfo() {
  const html = `
    <html>
      <head>
        <title>系統資訊</title>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial; padding: 20px; }
          .info { background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <h1>🔧 系統資訊</h1>
        
        <div class="info">
          <h3>部署資訊</h3>
          <table>
            <tr><th>項目</th><th>值</th></tr>
            <tr><td>部署時間</td><td>${new Date().toLocaleString('zh-TW')}</td></tr>
            <tr><td>Sheets ID</td><td>1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc</td></tr>
            <tr><td>Web App ID</td><td>AKfycbwTKT0l7fUwn0p4prFAjH1m7a1O4RVsz7Up-Du6uJSjN1tkxqZT8S1U8XtT4Hkcb-IK</td></tr>
            <tr><td>版本</td><td>最小化測試版</td></tr>
          </table>
        </div>
        
        <div class="info">
          <h3>測試連結</h3>
          <ul>
            <li><a href="?">首頁</a></li>
            <li><a href="?action=test">測試 Sheets 連接</a></li>
            <li><a href="https://docs.google.com/spreadsheets/d/1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc/edit" target="_blank">開啟 Google Sheets</a></li>
          </ul>
        </div>
      </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}
