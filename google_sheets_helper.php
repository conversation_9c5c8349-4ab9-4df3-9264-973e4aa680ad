<?php
// google_sheets_helper.php - Google Sheets API 輔助函數

/**
 * 使用 Google Sheets API 添加捐款記錄
 * 
 * @param array $record 捐款記錄資料
 * @return array 結果 ['success' => bool, 'error' => string]
 */
function addDonationRecord($record) {
    global $GOOGLE_SHEETS_CONFIG;
    
    try {
        // 方案1: 使用 Google Sheets API v4 (需要 Composer 和服務帳戶)
        if (file_exists($GOOGLE_SHEETS_CONFIG['service_account_key_file'])) {
            return addRecordViaAPI($record);
        }
        
        // 方案2: 使用 Google Apps Script Web App (推薦)
        return addRecordViaWebApp($record);
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 方案1: 使用 Google Sheets API v4 (需要服務帳戶認證)
 */
function addRecordViaAPI($record) {
    global $GOOGLE_SHEETS_CONFIG;
    
    // 這個方案需要 Google API PHP Client Library
    // 由於免費主機可能無法安裝 Composer，這裡提供基本框架
    
    /*
    require_once 'vendor/autoload.php';
    
    $client = new Google_Client();
    $client->setAuthConfig($GOOGLE_SHEETS_CONFIG['service_account_key_file']);
    $client->addScope(Google_Service_Sheets::SPREADSHEETS);
    
    $service = new Google_Service_Sheets($client);
    $spreadsheetId = $GOOGLE_SHEETS_CONFIG['spreadsheet_id'];
    $range = $GOOGLE_SHEETS_CONFIG['sheet_name'] . '!A:T';
    
    $values = [
        [
            $record['timestamp'],
            $record['merchant_order_no'],
            $record['amount'],
            $record['donor_name'],
            $record['donor_email'],
            $record['donor_phone'],
            $record['payment_type'],
            $record['receipt_option'],
            $record['item_general'],
            $record['item_book'],
            $record['item_life'],
            $record['item_clinic'],
            $record['item_building'],
            $record['item_jewels'],
            $record['status'],
            $record['trade_info_sent'],
            $record['trade_sha_sent'],
            $record['newebpay_response'],
            $record['updated_at'],
            $record['notes']
        ]
    ];
    
    $body = new Google_Service_Sheets_ValueRange([
        'values' => $values
    ]);
    
    $params = [
        'valueInputOption' => 'RAW'
    ];
    
    $result = $service->spreadsheets_values->append(
        $spreadsheetId, $range, $body, $params
    );
    
    return ['success' => true, 'updates' => $result->getUpdates()];
    */
    
    return ['success' => false, 'error' => 'API method not implemented'];
}

/**
 * 方案2: 使用 Google Apps Script Web App (推薦)
 */
function addRecordViaWebApp($record) {
    global $GOOGLE_SHEETS_CONFIG;
    
    // Google Apps Script Web App URL
    // 您需要建立一個 Google Apps Script 來接收這個請求
    $webAppUrl = 'https://script.google.com/macros/s/您的Google Apps Script ID/exec';
    
    // 準備 POST 資料
    $postData = array_merge($record, [
        'action' => 'add_record',
        'spreadsheet_id' => $GOOGLE_SHEETS_CONFIG['spreadsheet_id'],
        'sheet_name' => $GOOGLE_SHEETS_CONFIG['sheet_name']
    ]);
    
    // 使用 cURL 發送請求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webAppUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // 設定標頭
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'User-Agent: Donation-System/1.0'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // 記錄請求結果
    $logFile = 'webapp_request_log.txt';
    $logMessage = "WebApp request at: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "HTTP Code: " . $httpCode . "\n";
    $logMessage .= "Response: " . $response . "\n";
    if ($error) {
        $logMessage .= "cURL Error: " . $error . "\n";
    }
    $logMessage .= "----------------------------------------\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    
    if ($httpCode === 200 && !$error) {
        // 嘗試解析 JSON 回應
        $responseData = json_decode($response, true);
        if ($responseData && isset($responseData['success'])) {
            return $responseData;
        } else {
            // 如果不是 JSON 格式，假設成功
            return ['success' => true, 'response' => $response];
        }
    } else {
        return ['success' => false, 'error' => "HTTP $httpCode: $error"];
    }
}

/**
 * 方案3: 使用簡單的 HTTP POST 到 Google Forms (最簡單的方案)
 */
function addRecordViaGoogleForm($record) {
    // 這個方案使用 Google Forms 作為資料收集工具
    // 優點：最簡單，不需要 API 認證
    // 缺點：格式固定，較難自訂
    
    $formUrl = 'https://docs.google.com/forms/d/e/您的Google Form ID/formResponse';
    
    // Google Forms 的欄位 ID (需要從表單原始碼中取得)
    $formData = [
        'entry.123456789' => $record['merchant_order_no'], // 替換為實際的 entry ID
        'entry.987654321' => $record['amount'],
        'entry.111111111' => $record['donor_name'],
        'entry.222222222' => $record['donor_email'],
        // ... 其他欄位
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $formUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($formData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($httpCode === 200 && !$error) {
        return ['success' => true];
    } else {
        return ['success' => false, 'error' => "HTTP $httpCode: $error"];
    }
}

/**
 * 查詢捐款記錄 (透過 Google Apps Script)
 */
function getDonationRecord($merchantOrderNo) {
    global $GOOGLE_SHEETS_CONFIG;
    
    $webAppUrl = 'https://script.google.com/macros/s/您的Google Apps Script ID/exec';
    
    $queryData = [
        'action' => 'get_record',
        'merchant_order_no' => $merchantOrderNo,
        'spreadsheet_id' => $GOOGLE_SHEETS_CONFIG['spreadsheet_id'],
        'sheet_name' => $GOOGLE_SHEETS_CONFIG['sheet_name']
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webAppUrl . '?' . http_build_query($queryData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        return $data ? $data : ['success' => false, 'error' => 'Invalid response'];
    }
    
    return ['success' => false, 'error' => "HTTP $httpCode"];
}

/**
 * 更新捐款記錄狀態
 */
function updateDonationStatus($merchantOrderNo, $status, $responseData = '') {
    global $GOOGLE_SHEETS_CONFIG;
    
    $webAppUrl = 'https://script.google.com/macros/s/您的Google Apps Script ID/exec';
    
    $updateData = [
        'action' => 'update_status',
        'merchant_order_no' => $merchantOrderNo,
        'status' => $status,
        'newebpay_response' => $responseData,
        'updated_at' => date('Y-m-d H:i:s'),
        'spreadsheet_id' => $GOOGLE_SHEETS_CONFIG['spreadsheet_id'],
        'sheet_name' => $GOOGLE_SHEETS_CONFIG['sheet_name']
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webAppUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($updateData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        return $data ? $data : ['success' => true];
    }
    
    return ['success' => false, 'error' => "HTTP $httpCode"];
}
?>
