/**
 * Google Apps Script - 付款處理器
 * 這個腳本處理捐款表單提交和藍新金流整合
 * 檔案名稱：PaymentProcessor.gs
 */

// === 設定區域 ===
const PAYMENT_CONFIG = {
  // 藍新金流設定
  NEWEBPAY_KEY: 'vTxBcUTfd54kxzh01qborPcL5kJtMoxE',
  NEWEBPAY_IV: 'PEEDYyAVozYO6rDC',
  MERCHANT_ID: 'MS3757703701',
  VERSION: '2.0',
  GATEWAY_URL: 'https://core.newebpay.com/MPG/mpg_gateway',
  
  // Google Sheets 設定
  SPREADSHEET_ID: '請替換為您的 Google Sheets ID',
  SHEET_NAME: 'donation_records',
  
  // 系統設定
  NOTIFY_URL: '', // 將在 doGet/doPost 中動態設定
  RETURN_URL: '' // 將在 doGet/doPost 中動態設定
};

/**
 * 處理捐款表單提交
 */
function processDonation(formData) {
  try {
    // 驗證表單資料
    const validationResult = validateDonationForm(formData);
    if (!validationResult.valid) {
      return createErrorResponse(validationResult.error);
    }
    
    // 計算總金額
    const totalAmount = calculateTotalAmount(formData);
    if (totalAmount <= 0) {
      return createErrorResponse('捐款金額必須大於零');
    }
    
    // 生成訂單編號
    const merchantOrderNo = generateOrderNumber();
    
    // 準備藍新金流資料
    const paymentData = preparePaymentData(formData, merchantOrderNo, totalAmount);
    
    // 加密資料
    const encryptedData = encryptPaymentData(paymentData);
    
    // 儲存到 Google Sheets
    const donationRecord = createDonationRecord(formData, merchantOrderNo, totalAmount, encryptedData);
    const saveResult = saveDonationRecord(donationRecord);
    
    if (!saveResult.success) {
      logOperation('Save Failed', saveResult.error);
      // 繼續處理，但記錄錯誤
    }
    
    // 返回付款表單 HTML
    return createPaymentForm(encryptedData);
    
  } catch (error) {
    logOperation('Process Donation Error', error.toString());
    return createErrorResponse('處理捐款時發生錯誤，請稍後再試');
  }
}

/**
 * 驗證捐款表單
 */
function validateDonationForm(formData) {
  const required = ['donor_name', 'donor_email'];
  
  for (const field of required) {
    if (!formData[field] || formData[field].trim() === '') {
      return { valid: false, error: `請填寫${field}` };
    }
  }
  
  // 驗證 email 格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(formData.donor_email)) {
    return { valid: false, error: '請輸入有效的電子郵件地址' };
  }
  
  return { valid: true };
}

/**
 * 計算總捐款金額
 */
function calculateTotalAmount(formData) {
  const amounts = [
    'item_amount_general_expenses',
    'item_amount_book_printing',
    'item_amount_life_release',
    'item_amount_free_clinic',
    'item_amount_building_repair',
    'item_amount_offering_three_jewels'
  ];
  
  let total = 0;
  amounts.forEach(field => {
    const amount = parseInt(formData[field]) || 0;
    total += amount;
  });
  
  return total;
}

/**
 * 生成唯一訂單編號
 */
function generateOrderNumber() {
  return 'CDL' + Date.now() + Math.floor(Math.random() * 1000);
}

/**
 * 準備藍新金流付款資料
 */
function preparePaymentData(formData, merchantOrderNo, amount) {
  // 動態設定 URL (使用當前 Web App URL)
  const currentUrl = ScriptApp.getService().getUrl();
  PAYMENT_CONFIG.NOTIFY_URL = currentUrl + '?action=notify';
  PAYMENT_CONFIG.RETURN_URL = currentUrl + '?action=return';
  
  return {
    'MerchantID': PAYMENT_CONFIG.MERCHANT_ID,
    'RespondType': 'String',
    'TimeStamp': Math.floor(Date.now() / 1000),
    'Version': PAYMENT_CONFIG.VERSION,
    'MerchantOrderNo': merchantOrderNo,
    'Amt': amount,
    'ItemDesc': '慈光圖書館捐款',
    'NotifyURL': PAYMENT_CONFIG.NOTIFY_URL,
    'ReturnURL': PAYMENT_CONFIG.RETURN_URL,
    'Email': formData.donor_email,
    'EmailModify': 1,
    'LoginType': 0,
    'CREDIT': 1,
    'UNIONPAY': 1,
    'CVS': 1,
    'TAIWANPAY': 1,
    'LINEPAY': 1,
    'GOOGLEPAY': 1,
    'WEBATM': 1,
    'VACC': 1,
    'FOREIGN': 1
  };
}

/**
 * 加密付款資料
 */
function encryptPaymentData(data) {
  // 建立查詢字串
  const queryString = Object.keys(data)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
    .join('&');
  
  // AES 加密 (使用 Google Apps Script 的 Utilities)
  const encrypted = Utilities.base64Encode(
    Utilities.computeHmacSha256Signature(queryString, PAYMENT_CONFIG.NEWEBPAY_KEY)
  );
  
  // 注意：Google Apps Script 的加密功能有限
  // 實際部署時可能需要使用外部加密服務
  const tradeInfo = Utilities.base64Encode(queryString); // 簡化版本
  
  // 計算 SHA-256 雜湊
  const hashString = `HashKey=${PAYMENT_CONFIG.NEWEBPAY_KEY}&${tradeInfo}&HashIV=${PAYMENT_CONFIG.NEWEBPAY_IV}`;
  const tradeSha = Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, hashString)
    .map(byte => (byte < 0 ? byte + 256 : byte).toString(16).padStart(2, '0'))
    .join('').toUpperCase();
  
  return {
    tradeInfo: tradeInfo,
    tradeSha: tradeSha,
    merchantId: PAYMENT_CONFIG.MERCHANT_ID,
    version: PAYMENT_CONFIG.VERSION
  };
}

/**
 * 建立捐款記錄
 */
function createDonationRecord(formData, merchantOrderNo, amount, encryptedData) {
  return {
    timestamp: new Date().toLocaleString('zh-TW'),
    merchant_order_no: merchantOrderNo,
    amount: amount,
    donor_name: formData.donor_name || '',
    donor_email: formData.donor_email || '',
    donor_phone: formData.donor_phone || '',
    payment_type: formData.payment_type || '',
    receipt_option: formData.receipt_option || '',
    item_general: parseInt(formData.item_amount_general_expenses) || 0,
    item_book: parseInt(formData.item_amount_book_printing) || 0,
    item_life: parseInt(formData.item_amount_life_release) || 0,
    item_clinic: parseInt(formData.item_amount_free_clinic) || 0,
    item_building: parseInt(formData.item_amount_building_repair) || 0,
    item_jewels: parseInt(formData.item_amount_offering_three_jewels) || 0,
    status: 'Pending',
    trade_info_sent: encryptedData.tradeInfo,
    trade_sha_sent: encryptedData.tradeSha,
    newebpay_response: '',
    updated_at: new Date().toLocaleString('zh-TW'),
    notes: ''
  };
}

/**
 * 儲存捐款記錄到 Google Sheets
 */
function saveDonationRecord(record) {
  try {
    const spreadsheet = SpreadsheetApp.openById(PAYMENT_CONFIG.SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(PAYMENT_CONFIG.SHEET_NAME);
    
    if (!sheet) {
      return { success: false, error: 'Sheet not found' };
    }
    
    // 轉換為陣列格式
    const rowData = [
      record.timestamp,
      record.merchant_order_no,
      record.amount,
      record.donor_name,
      record.donor_email,
      record.donor_phone,
      record.payment_type,
      record.receipt_option,
      record.item_general,
      record.item_book,
      record.item_life,
      record.item_clinic,
      record.item_building,
      record.item_jewels,
      record.status,
      record.trade_info_sent,
      record.trade_sha_sent,
      record.newebpay_response,
      record.updated_at,
      record.notes
    ];
    
    sheet.appendRow(rowData);
    
    logOperation('Record Saved', { 
      merchant_order_no: record.merchant_order_no,
      amount: record.amount 
    });
    
    return { success: true };
    
  } catch (error) {
    return { success: false, error: error.toString() };
  }
}

/**
 * 建立付款表單 HTML
 */
function createPaymentForm(encryptedData) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>導向支付頁面</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .loading { font-size: 18px; color: #666; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; 
                   border-radius: 50%; width: 40px; height: 40px; 
                   animation: spin 2s linear infinite; margin: 20px auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
      </style>
    </head>
    <body>
      <div class="spinner"></div>
      <p class="loading">正在安全導向支付頁面，請稍候...</p>
      
      <form id="payment-form" method="post" action="${PAYMENT_CONFIG.GATEWAY_URL}">
        <input type="hidden" name="MerchantID" value="${encryptedData.merchantId}">
        <input type="hidden" name="Version" value="${encryptedData.version}">
        <input type="hidden" name="TradeInfo" value="${encryptedData.tradeInfo}">
        <input type="hidden" name="TradeSha" value="${encryptedData.tradeSha}">
      </form>
      
      <script>
        setTimeout(function() {
          document.getElementById("payment-form").submit();
        }, 2000);
      </script>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}

/**
 * 建立錯誤回應
 */
function createErrorResponse(message) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>錯誤</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .error { color: #dc3545; font-size: 18px; }
        .button { display: inline-block; padding: 10px 20px; background: #007bff; 
                  color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
      </style>
    </head>
    <body>
      <h2>處理錯誤</h2>
      <p class="error">${message}</p>
      <a href="javascript:history.back()" class="button">返回</a>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}
