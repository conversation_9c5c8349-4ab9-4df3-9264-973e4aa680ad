# 純 Google 服務捐款系統解決方案

## 系統架構概述

由於免費虛擬主機會阻擋藍新金流的請求，我們採用完全基於 Google 服務的解決方案：

```
用戶填寫表單 → Google Apps Script (處理) → Google Sheets (儲存)
                        ↓
                導向藍新金流付款
                        ↓
        藍新金流通知 → Google Apps Script (通知處理) → 更新 Google Sheets
```

## 第一階段：建立 Google Apps Script 專案

### 1. 建立主要的 Google Apps Script

1. 前往 [Google Apps Script](https://script.google.com)
2. 點擊「新專案」
3. 將專案命名為「慈光圖書館捐款系統」

### 2. 建立多個腳本檔案

#### 檔案 1: Code.gs (主要處理邏輯)
```javascript
// 將 google_apps_script_database.js 的內容複製到這裡
```

#### 檔案 2: PaymentProcessor.gs (付款處理)
```javascript
// 將下面提供的付款處理程式碼複製到這裡
```

#### 檔案 3: NotifyHandler.gs (通知處理)
```javascript
// 將 google_apps_script_notify.js 的內容複製到這裡
```

#### 檔案 4: CryptoUtils.gs (加密工具)
```javascript
// 將下面提供的加密工具程式碼複製到這裡
```

## 第二階段：建立 Google Sheets 資料庫

### 1. 建立 Google Sheets
1. 前往 [Google Sheets](https://sheets.google.com)
2. 建立新試算表：「慈光圖書館捐款記錄」
3. 複製 Sheets ID (URL 中的長字串)

### 2. 設定工作表結構
按照之前提供的 `google_sheets_structure.md` 設定欄位

## 第三階段：建立捐款表單

### 選項 A: Google Forms (最簡單)
1. 建立 Google Forms 表單
2. 設定所有必要欄位
3. 連結到 Google Apps Script 處理

### 選項 B: 自訂 HTML 表單 (推薦)
使用 Google Apps Script 的 HTML Service 建立自訂表單

## 第四階段：部署和測試

### 1. 部署 Google Apps Script
1. 點擊「部署」→「新增部署作業」
2. 類型：「網頁應用程式」
3. 執行身分：「我」
4. 存取權限：「任何人」
5. 複製 Web App URL

### 2. 設定藍新金流
1. 登入藍新金流商店後台
2. 設定 NotifyURL 為您的 Google Apps Script URL
3. 設定 ReturnURL 為您的結果頁面 URL

## 測試流程

### 階段 1: 基本功能測試
1. 測試 Google Apps Script API
2. 測試 Google Sheets 讀寫
3. 測試加密解密功能

### 階段 2: 整合測試
1. 測試完整的捐款流程
2. 模擬藍新金流通知
3. 驗證資料正確性

### 階段 3: 真實環境測試
1. 小額真實交易測試
2. 監控日誌和錯誤
3. 確認通知機制正常

## 優勢

✅ **完全免費** - 使用 Google 免費服務
✅ **高穩定性** - Google 雲端服務保證
✅ **無主機限制** - 不受免費虛擬主機限制
✅ **易於管理** - 視覺化介面管理
✅ **自動備份** - Google 自動版本控制

## 注意事項

⚠️ **重要提醒**：
1. 所有敏感資料都在 Google 雲端處理
2. 確保 Google 帳戶安全性
3. 定期備份重要設定
4. 遵循 Google 服務條款

## 下一步

請按照以下順序進行：
1. 建立 Google Apps Script 專案
2. 設定 Google Sheets 資料庫
3. 部署並測試基本功能
4. 整合藍新金流測試環境
5. 進行完整的端到端測試
