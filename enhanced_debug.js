/**
 * 強化調試版本 - Code.gs
 * 專門解決 POST 請求問題
 */

// === 系統設定 ===
const DEBUG_CONFIG = {
  SPREADSHEET_ID: '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc',
  SHEET_NAME: 'donation_records',
  LOG_SHEET_NAME: 'system_logs'
};

/**
 * 處理 GET 請求
 */
function doGet(e) {
  try {
    const action = e.parameter.action || 'form';
    
    debugLog('GET Request', { 
      action: action, 
      parameters: e.parameters,
      userAgent: e.parameter.userAgent || 'unknown'
    });
    
    switch (action) {
      case 'form':
        return showEnhancedDebugForm();
      case 'test':
        return handleDebugTest();
      case 'posttest':
        return handlePostTest(e);
      default:
        return showEnhancedDebugForm();
    }
    
  } catch (error) {
    debugLog('GET Error', error.toString());
    return createDebugError('GET 錯誤: ' + error.toString());
  }
}

/**
 * 處理 POST 請求
 */
function doPost(e) {
  try {
    debugLog('POST Request Started', {
      timestamp: new Date().toISOString(),
      hasParameters: !!e.parameters,
      parameterKeys: e.parameters ? Object.keys(e.parameters) : []
    });
    
    if (!e.parameters) {
      debugLog('POST Error', 'No parameters received');
      return createDebugError('沒有接收到表單資料');
    }
    
    // 記錄所有接收到的參數
    const params = {};
    Object.keys(e.parameters).forEach(key => {
      params[key] = e.parameters[key][0];
    });
    
    debugLog('POST Parameters Received', params);
    
    // 驗證必要欄位
    if (!params.donor_name || params.donor_name.trim() === '') {
      debugLog('Validation Error', 'Missing or empty donor_name');
      return createDebugError('缺少捐款人姓名');
    }
    
    if (!params.donor_email || params.donor_email.trim() === '') {
      debugLog('Validation Error', 'Missing or empty donor_email');
      return createDebugError('缺少電子郵件');
    }
    
    // 計算金額
    const amount = calculateDebugAmount(params);
    debugLog('Amount Calculated', { amount: amount, params: params });
    
    if (amount <= 0) {
      debugLog('Validation Error', 'Invalid amount: ' + amount);
      return createDebugError('捐款金額必須大於零，目前金額: ' + amount);
    }
    
    // 生成訂單編號
    const orderNo = 'DEBUG' + Date.now() + '_' + Math.floor(Math.random() * 1000);
    debugLog('Order Generated', { orderNo: orderNo });
    
    // 嘗試儲存記錄
    debugLog('Attempting to save record', 'Starting save process');
    const saveResult = saveDebugRecord(params, orderNo, amount);
    debugLog('Save Completed', saveResult);
    
    if (!saveResult.success) {
      return createDebugError('儲存失敗: ' + saveResult.error);
    }
    
    // 返回成功頁面
    debugLog('Returning success page', { orderNo: orderNo, amount: amount });
    return createDebugSuccess(orderNo, amount);
    
  } catch (error) {
    debugLog('POST Exception', {
      error: error.toString(),
      stack: error.stack || 'No stack trace'
    });
    return createDebugError('POST 處理異常: ' + error.toString());
  }
}

/**
 * 顯示強化調試表單
 */
function showEnhancedDebugForm() {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>強化調試版捐款表單</title>
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .submit-btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; width: 100%; font-size: 16px; }
        .submit-btn:hover { background: #0056b3; }
        .submit-btn:disabled { background: #6c757d; cursor: not-allowed; }
        .debug-info { background: #e7f3ff; border: 1px solid #b3d9ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #ffe6e6; border: 1px solid #ffb3b3; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .total { background: #e8f5e8; border: 1px solid #b3e6b3; padding: 10px; border-radius: 4px; margin: 10px 0; font-weight: bold; }
        .status { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🔧 強化調試版捐款表單</h1>
        
        <div class="debug-info">
          <strong>調試資訊：</strong><br>
          時間：${new Date().toLocaleString('zh-TW')}<br>
          版本：強化調試版 v2.0<br>
          狀態：等待表單提交<br>
          瀏覽器：<span id="browserInfo"></span>
        </div>
        
        <div id="statusMessage" class="status" style="display: none;"></div>
        <div id="errorMessage" class="error" style="display: none;"></div>
        
        <form id="debugForm" method="post">
          <div class="form-group">
            <label for="donor_name">捐款人姓名 *</label>
            <input type="text" id="donor_name" name="donor_name" required>
          </div>
          
          <div class="form-group">
            <label for="donor_email">電子郵件 *</label>
            <input type="email" id="donor_email" name="donor_email" required>
          </div>
          
          <div class="form-group">
            <label for="donor_phone">聯絡電話</label>
            <input type="tel" id="donor_phone" name="donor_phone">
          </div>
          
          <div class="form-group">
            <label for="amount_general">一般經費</label>
            <input type="number" id="amount_general" name="item_amount_general_expenses" min="0" value="0" onchange="calculateTotal()">
          </div>
          
          <div class="form-group">
            <label for="amount_book">助印弘法</label>
            <input type="number" id="amount_book" name="item_amount_book_printing" min="0" value="0" onchange="calculateTotal()">
          </div>
          
          <div class="total">
            總金額：NT$ <span id="totalAmount">0</span>
          </div>
          
          <button type="submit" class="submit-btn" id="submitBtn">提交測試</button>
        </form>
        
        <div class="debug-info">
          <strong>測試連結：</strong><br>
          <a href="?test=true" target="_blank">系統測試</a> |
          <a href="?posttest=true" target="_blank">POST 測試</a>
        </div>
      </div>
      
      <script>
        // 顯示瀏覽器資訊
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').pop();
        
        function calculateTotal() {
          const general = parseInt(document.getElementById('amount_general').value) || 0;
          const book = parseInt(document.getElementById('amount_book').value) || 0;
          const total = general + book;
          document.getElementById('totalAmount').textContent = total.toLocaleString();
        }
        
        function showStatus(message) {
          const statusDiv = document.getElementById('statusMessage');
          statusDiv.textContent = message;
          statusDiv.style.display = 'block';
          hideError();
        }
        
        function showError(message) {
          const errorDiv = document.getElementById('errorMessage');
          errorDiv.textContent = message;
          errorDiv.style.display = 'block';
          hideStatus();
        }
        
        function hideStatus() {
          document.getElementById('statusMessage').style.display = 'none';
        }
        
        function hideError() {
          document.getElementById('errorMessage').style.display = 'none';
        }
        
        document.getElementById('debugForm').addEventListener('submit', function(e) {
          console.log('Form submit triggered at:', new Date().toISOString());
          hideError();
          hideStatus();
          
          // 基本驗證
          const name = document.getElementById('donor_name').value.trim();
          const email = document.getElementById('donor_email').value.trim();
          const total = parseInt(document.getElementById('totalAmount').textContent.replace(/,/g, ''));
          
          console.log('Form data:', { name, email, total });
          
          if (!name) {
            e.preventDefault();
            showError('請輸入捐款人姓名');
            console.log('Validation failed: missing name');
            return false;
          }
          
          if (!email) {
            e.preventDefault();
            showError('請輸入電子郵件');
            console.log('Validation failed: missing email');
            return false;
          }
          
          if (total <= 0) {
            e.preventDefault();
            showError('請輸入捐款金額');
            console.log('Validation failed: invalid amount');
            return false;
          }
          
          // 顯示提交狀態
          const submitBtn = document.getElementById('submitBtn');
          submitBtn.textContent = '處理中...';
          submitBtn.disabled = true;
          
          showStatus('正在提交表單，請稍候...');
          
          console.log('Form validation passed, submitting...');
          
          // 設定超時檢查
          setTimeout(function() {
            if (submitBtn.disabled) {
              showError('提交超時，請檢查網路連接或重試');
              submitBtn.textContent = '提交測試';
              submitBtn.disabled = false;
            }
          }, 30000); // 30秒超時
          
          return true;
        });
        
        // 初始化
        calculateTotal();
        console.log('Enhanced debug form loaded at:', new Date().toISOString());
      </script>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html)
    .setTitle('強化調試版捐款表單')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * POST 測試處理
 */
function handlePostTest(e) {
  try {
    debugLog('POST Test', 'Manual POST test triggered');
    
    // 模擬 POST 資料
    const mockParams = {
      donor_name: ['測試用戶'],
      donor_email: ['<EMAIL>'],
      donor_phone: ['0912345678'],
      item_amount_general_expenses: ['1000'],
      item_amount_book_printing: ['500']
    };
    
    const mockEvent = {
      parameters: mockParams
    };
    
    return doPost(mockEvent);
    
  } catch (error) {
    debugLog('POST Test Error', error.toString());
    return createDebugError('POST 測試錯誤: ' + error.toString());
  }
}

/**
 * 計算調試金額
 */
function calculateDebugAmount(params) {
  const general = parseInt(params.item_amount_general_expenses) || 0;
  const book = parseInt(params.item_amount_book_printing) || 0;
  debugLog('Amount Calculation', { 
    general: general, 
    book: book, 
    total: general + book,
    rawGeneral: params.item_amount_general_expenses,
    rawBook: params.item_amount_book_printing
  });
  return general + book;
}

/**
 * 儲存調試記錄
 */
function saveDebugRecord(params, orderNo, amount) {
  try {
    debugLog('Save Record Start', { orderNo: orderNo, amount: amount });
    
    const spreadsheet = SpreadsheetApp.openById(DEBUG_CONFIG.SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(DEBUG_CONFIG.SHEET_NAME);
    
    if (!sheet) {
      throw new Error('Sheet not found: ' + DEBUG_CONFIG.SHEET_NAME);
    }
    
    const record = [
      new Date().toLocaleString('zh-TW'),
      orderNo,
      amount,
      params.donor_name || '',
      params.donor_email || '',
      params.donor_phone || '',
      'DEBUG',  // payment_type
      'debug_test',  // receipt_option
      parseInt(params.item_amount_general_expenses) || 0,
      parseInt(params.item_amount_book_printing) || 0,
      0, 0, 0, 0,  // 其他項目
      'Debug Test',
      '', '', '',  // 藍新相關欄位
      new Date().toLocaleString('zh-TW'),
      'Enhanced debug record'
    ];
    
    debugLog('Record Data', record);
    
    sheet.appendRow(record);
    
    debugLog('Save Record Success', 'Record saved to sheet');
    
    return { success: true, message: 'Record saved successfully' };
    
  } catch (error) {
    debugLog('Save Record Error', error.toString());
    return { success: false, error: error.toString() };
  }
}

/**
 * 調試日誌
 */
function debugLog(type, data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(DEBUG_CONFIG.SPREADSHEET_ID);
    const logSheet = spreadsheet.getSheetByName(DEBUG_CONFIG.LOG_SHEET_NAME);
    
    logSheet.appendRow([
      new Date(),
      'ENHANCED_DEBUG: ' + type,
      typeof data === 'object' ? JSON.stringify(data) : data,
      'Enhanced Debug Session'
    ]);
    
  } catch (error) {
    console.error('Debug logging failed:', error);
  }
}

/**
 * 調試測試
 */
function handleDebugTest() {
  try {
    const spreadsheet = SpreadsheetApp.openById(DEBUG_CONFIG.SPREADSHEET_ID);
    
    const result = {
      status: 'ENHANCED_DEBUG_OK',
      timestamp: new Date().toISOString(),
      spreadsheet_name: spreadsheet.getName(),
      sheets: spreadsheet.getSheets().map(sheet => sheet.getName()),
      message: '強化調試測試成功'
    };
    
    debugLog('Enhanced Debug Test', result);
    
    return ContentService
      .createTextOutput(JSON.stringify(result, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    const errorResult = {
      status: 'ENHANCED_DEBUG_ERROR',
      error: error.toString()
    };
    
    return ContentService
      .createTextOutput(JSON.stringify(errorResult, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 建立調試成功頁面
 */
function createDebugSuccess(orderNo, amount) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>強化調試成功</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; text-align: center; }
        .success { color: green; font-size: 24px; margin: 20px 0; }
        .info { background: #e8f5e8; border: 1px solid #b3e6b3; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .button { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 10px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="success">✅ 強化調試測試成功！</div>
        
        <div class="info">
          <h3>處理結果</h3>
          <p><strong>訂單編號：</strong>${orderNo}</p>
          <p><strong>金額：</strong>NT$ ${amount}</p>
          <p><strong>時間：</strong>${new Date().toLocaleString('zh-TW')}</p>
        </div>
        
        <div class="info">
          <h3>系統狀態</h3>
          <p>✅ 表單驗證正常</p>
          <p>✅ POST 請求處理正常</p>
          <p>✅ 資料庫寫入正常</p>
          <p>✅ 頁面渲染正常</p>
          <p>✅ 強化調試功能正常</p>
        </div>
        
        <a href="?" class="button">返回表單</a>
        <a href="?test=true" class="button">系統測試</a>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}

/**
 * 建立調試錯誤頁面
 */
function createDebugError(message) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>強化調試錯誤</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; text-align: center; }
        .error { color: red; font-size: 18px; margin: 20px 0; }
        .info { background: #ffe6e6; border: 1px solid #ffb3b3; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .button { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 10px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="error">❌ 強化調試錯誤</div>
        
        <div class="info">
          <h3>錯誤訊息</h3>
          <p>${message}</p>
          <p><strong>時間：</strong>${new Date().toLocaleString('zh-TW')}</p>
        </div>
        
        <a href="?" class="button">返回表單</a>
        <a href="?posttest=true" class="button">POST 測試</a>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}
