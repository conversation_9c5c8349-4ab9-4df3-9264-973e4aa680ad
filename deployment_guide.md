# 純 Google 服務捐款系統部署指南

## 🚀 快速部署步驟

### 第一步：建立 Google Sheets 資料庫

1. **建立新的 Google Sheets**
   - 前往 [Google Sheets](https://sheets.google.com)
   - 點擊「空白」建立新試算表
   - 命名為「慈光圖書館捐款記錄」

2. **設定資料結構**
   - 在第一列輸入以下標題（A1 到 T1）：
   ```
   timestamp | merchant_order_no | amount | donor_name | donor_email | donor_phone | payment_type | receipt_option | item_general | item_book | item_life | item_clinic | item_building | item_jewels | status | trade_info_sent | trade_sha_sent | newebpay_response | updated_at | notes
   ```

3. **複製 Sheets ID**
   - 從 URL 複製 Sheets ID（`/d/` 和 `/edit` 之間的長字串）
   - 例如：`1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms`

### 第二步：建立 Google Apps Script

1. **建立新專案**
   - 前往 [Google Apps Script](https://script.google.com)
   - 點擊「新專案」
   - 命名為「慈光圖書館捐款系統」

2. **建立腳本檔案**

   **檔案 1：Code.gs（主控制器）**
   ```javascript
   // 將 google_apps_script_main.js 的內容完整複製到這裡
   // 記得修改第 9 行的 SPREADSHEET_ID
   ```

   **檔案 2：PaymentProcessor.gs（付款處理）**
   ```javascript
   // 將 google_apps_script_payment.js 的內容完整複製到這裡
   // 記得修改第 18 行的 SPREADSHEET_ID
   ```

   **檔案 3：NotifyHandler.gs（通知處理）**
   ```javascript
   // 將 google_apps_script_notify.js 的內容完整複製到這裡
   // 記得修改第 15 行的 SPREADSHEET_ID
   ```

   **檔案 4：DatabaseAPI.gs（資料庫操作）**
   ```javascript
   // 將 google_apps_script_database.js 的內容完整複製到這裡
   // 記得修改第 9 行的 SPREADSHEET_ID
   ```

3. **更新設定**
   - 在每個檔案中找到 `請替換為您的 Google Sheets ID`
   - 替換為您在第一步複製的 Sheets ID

### 第三步：部署 Web App

1. **部署設定**
   - 在 Google Apps Script 中點擊「部署」
   - 選擇「新增部署作業」
   - 類型：「網頁應用程式」
   - 說明：「慈光圖書館捐款系統 v1.0」
   - 執行身分：「我」
   - 存取權限：「任何人」（重要！）

2. **複製 Web App URL**
   - 部署完成後會得到一個 URL
   - 格式：`https://script.google.com/macros/s/[ID]/exec`
   - 這就是您的捐款系統網址

### 第四步：測試系統

1. **基本功能測試**
   ```
   https://script.google.com/macros/s/[您的ID]/exec?test=true
   ```
   應該返回系統狀態 JSON

2. **捐款表單測試**
   ```
   https://script.google.com/macros/s/[您的ID]/exec
   ```
   應該顯示捐款表單

3. **填寫測試捐款**
   - 填寫表單並提交
   - 檢查 Google Sheets 是否有新記錄
   - 注意：此時會導向藍新金流，但因為是測試資料，付款會失敗

### 第五步：設定藍新金流

1. **登入藍新金流商店後台**
   - 前往「系統設定」→「支付設定」

2. **設定通知 URL**
   ```
   https://script.google.com/macros/s/[您的ID]/exec?action=notify
   ```

3. **設定返回 URL**
   ```
   https://script.google.com/macros/s/[您的ID]/exec?action=return
   ```

4. **測試環境設定**
   - 先在測試環境進行完整測試
   - 確認通知機制正常運作

## 🔧 進階設定

### 自訂網域（可選）

如果您有自己的網域，可以：
1. 設定 CNAME 記錄指向 Google Apps Script
2. 或使用 URL 轉發功能

### 安全性增強

1. **IP 白名單**
   - 在 NotifyHandler.gs 中加入藍新金流 IP 驗證

2. **資料加密**
   - 敏感資料可以額外加密儲存

3. **存取日誌**
   - 系統會自動記錄所有操作到 system_logs 工作表

## 📊 監控和維護

### 日誌檢查
- **system_logs** 工作表：系統操作記錄
- **notification_logs** 工作表：付款通知記錄
- **operation_logs** 工作表：資料庫操作記錄

### 定期備份
- Google Sheets 自動備份
- 建議定期匯出重要資料

### 效能監控
- 監控 Google Apps Script 執行時間
- 檢查配額使用情況

## ❗ 重要注意事項

1. **Google Apps Script 限制**
   - 每日執行時間限制：6 小時
   - 單次執行時間限制：6 分鐘
   - 觸發器限制：20 個

2. **資料安全**
   - 所有資料都在您的 Google 帳戶中
   - 確保帳戶安全性
   - 定期檢查存取權限

3. **服務條款**
   - 遵循 Google 服務條款
   - 遵循藍新金流服務條款

## 🆘 故障排除

### 常見問題

**Q: 表單提交後出現錯誤**
A: 檢查 SPREADSHEET_ID 是否正確設定

**Q: 無法接收藍新金流通知**
A: 確認部署時存取權限設為「任何人」

**Q: Google Sheets 沒有新資料**
A: 檢查 system_logs 工作表的錯誤訊息

### 測試工具

使用以下 URL 進行各種測試：
- 系統狀態：`?test=true`
- 捐款表單：`?action=form`
- 模擬通知：`?action=test_notify`

## 🎉 部署完成

完成以上步驟後，您就擁有了一個：
- ✅ 完全免費的捐款系統
- ✅ 不受虛擬主機限制
- ✅ 企業級穩定性
- ✅ 即時資料管理
- ✅ 自動備份機制

您的捐款系統網址：
```
https://script.google.com/macros/s/[您的ID]/exec
```

現在可以開始接受捐款了！
