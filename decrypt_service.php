<?php
// decrypt_service.php - 解密服務，協助 Google Apps Script 處理藍新金流資料

// 設定 CORS 標頭，允許 Google Apps Script 調用
header('Access-Control-Allow-Origin: https://script.google.com');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

// 處理 preflight 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允許 POST 請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// 安全設定
$ALLOWED_ORIGINS = [
    'https://script.google.com',
    'https://script.googleusercontent.com'
];

// 驗證請求來源
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (!in_array($origin, $ALLOWED_ORIGINS)) {
    // 記錄可疑請求
    error_log("Unauthorized decrypt request from: " . $origin);
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit();
}

// 取得 POST 資料
$tradeInfo = $_POST['tradeInfo'] ?? '';
$key = $_POST['key'] ?? '';
$iv = $_POST['iv'] ?? '';

// 驗證必要參數
if (empty($tradeInfo) || empty($key) || empty($iv)) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required parameters']);
    exit();
}

// 驗證金鑰 (額外安全檢查)
$expectedKey = "vTxBcUTfd54kxzh01qborPcL5kJtMoxE"; // 您的實際金鑰
$expectedIv = "PEEDYyAVozYO6rDC"; // 您的實際 IV

if ($key !== $expectedKey || $iv !== $expectedIv) {
    error_log("Invalid key/iv in decrypt request");
    http_response_code(403);
    echo json_encode(['error' => 'Invalid credentials']);
    exit();
}

try {
    // 根據藍新金流官方技術手冊進行 AES-256-CBC 解密
    // 步驟1: TradeInfo 是 hex 編碼的加密資料，需要先轉換為二進位
    $encryptedData = hex2bin($tradeInfo);

    if ($encryptedData === false) {
        throw new Exception('Invalid hex data format');
    }

    // 步驟2: 使用 AES-256-CBC 解密，符合藍新金流規範
    $decryptedData = openssl_decrypt($encryptedData, "AES-256-CBC", $key, OPENSSL_RAW_DATA, $iv);

    if ($decryptedData === false) {
        $opensslError = openssl_error_string();
        throw new Exception('AES decryption failed: ' . $opensslError);
    }

    // 步驟3: 解析 URL 編碼的查詢字串格式資料
    $params = [];
    parse_str($decryptedData, $params);

    // 驗證必要欄位是否存在（根據藍新金流規範）
    $requiredFields = ['Status', 'MerchantOrderNo', 'Amt'];
    foreach ($requiredFields as $field) {
        if (!isset($params[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }

    // 記錄解密成功的詳細資訊
    $logFile = 'decrypt_service_log.txt';
    $logMessage = "=== Decrypt Success ===\n";
    $logMessage .= "Timestamp: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "MerchantOrderNo: " . ($params['MerchantOrderNo'] ?? 'Unknown') . "\n";
    $logMessage .= "Status: " . ($params['Status'] ?? 'Unknown') . "\n";
    $logMessage .= "Amount: " . ($params['Amt'] ?? 'Unknown') . "\n";
    $logMessage .= "TradeNo: " . ($params['TradeNo'] ?? 'N/A') . "\n";
    $logMessage .= "PaymentType: " . ($params['PaymentType'] ?? 'N/A') . "\n";
    $logMessage .= "All Params: " . print_r($params, true) . "\n";
    $logMessage .= "=== End Decrypt Success ===\n\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

    // 返回符合規範的解密結果
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => $params,
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => 'Decryption successful'
    ]);

} catch (Exception $e) {
    // 記錄錯誤
    error_log("Decrypt service error: " . $e->getMessage());
    
    $logFile = 'decrypt_service_error_log.txt';
    $logMessage = "Decrypt error at: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "Error: " . $e->getMessage() . "\n";
    $logMessage .= "TradeInfo length: " . strlen($tradeInfo) . "\n";
    $logMessage .= "----------------------------------------\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Decryption failed'
    ]);
}
?>
