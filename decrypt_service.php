<?php
// decrypt_service.php - 解密服務，協助 Google Apps Script 處理藍新金流資料

// 設定 CORS 標頭，允許 Google Apps Script 調用
header('Access-Control-Allow-Origin: https://script.google.com');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

// 處理 preflight 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允許 POST 請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// 安全設定
$ALLOWED_ORIGINS = [
    'https://script.google.com',
    'https://script.googleusercontent.com'
];

// 驗證請求來源
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (!in_array($origin, $ALLOWED_ORIGINS)) {
    // 記錄可疑請求
    error_log("Unauthorized decrypt request from: " . $origin);
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit();
}

// 取得 POST 資料
$tradeInfo = $_POST['tradeInfo'] ?? '';
$key = $_POST['key'] ?? '';
$iv = $_POST['iv'] ?? '';

// 驗證必要參數
if (empty($tradeInfo) || empty($key) || empty($iv)) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required parameters']);
    exit();
}

// 驗證金鑰 (額外安全檢查)
$expectedKey = "vTxBcUTfd54kxzh01qborPcL5kJtMoxE"; // 您的實際金鑰
$expectedIv = "PEEDYyAVozYO6rDC"; // 您的實際 IV

if ($key !== $expectedKey || $iv !== $expectedIv) {
    error_log("Invalid key/iv in decrypt request");
    http_response_code(403);
    echo json_encode(['error' => 'Invalid credentials']);
    exit();
}

try {
    // 解密 AES-256-CBC 資料
    // TradeInfo 是 hex 編碼的加密資料
    $encryptedData = hex2bin($tradeInfo);
    
    if ($encryptedData === false) {
        throw new Exception('Invalid hex data');
    }
    
    // 使用 OpenSSL 解密
    $decryptedData = openssl_decrypt($encryptedData, "AES-256-CBC", $key, OPENSSL_RAW_DATA, $iv);
    
    if ($decryptedData === false) {
        throw new Exception('Decryption failed');
    }
    
    // 解析查詢字串格式的資料
    $params = [];
    parse_str($decryptedData, $params);
    
    // 記錄解密成功
    $logFile = 'decrypt_service_log.txt';
    $logMessage = "Decrypt success at: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "MerchantOrderNo: " . ($params['MerchantOrderNo'] ?? 'Unknown') . "\n";
    $logMessage .= "Status: " . ($params['Status'] ?? 'Unknown') . "\n";
    $logMessage .= "Decrypted params: " . print_r($params, true) . "\n";
    $logMessage .= "----------------------------------------\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    
    // 返回解密後的資料
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => $params
    ]);
    
} catch (Exception $e) {
    // 記錄錯誤
    error_log("Decrypt service error: " . $e->getMessage());
    
    $logFile = 'decrypt_service_error_log.txt';
    $logMessage = "Decrypt error at: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "Error: " . $e->getMessage() . "\n";
    $logMessage .= "TradeInfo length: " . strlen($tradeInfo) . "\n";
    $logMessage .= "----------------------------------------\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Decryption failed'
    ]);
}
?>
