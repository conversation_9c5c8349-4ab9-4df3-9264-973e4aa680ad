<?php
// webhook_forwarder.php - Webhook forwarder for bypassing hosting restrictions

// Configuration
$TARGET_URL = 'https://amtbtc.infy.uk/donate/notify_url.php'; // Your actual notify URL
$SECRET_KEY = 'your_secret_webhook_key_here'; // Change this to a secure random string

// Log file for debugging
$logFile = 'webhook_forwarder_log.txt';

// Function to log messages
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// Verify secret key if provided
if (isset($_GET['secret']) && $_GET['secret'] !== $SECRET_KEY) {
    http_response_code(403);
    logMessage("Unauthorized access attempt from " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown'));
    die('Unauthorized');
}

// Log incoming request
logMessage("Webhook forwarder received request from " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown'));

// Get all POST data
$postData = $_POST;
$rawInput = file_get_contents('php://input');

logMessage("POST Data: " . print_r($postData, true));
logMessage("Raw Input: " . $rawInput);

// Prepare data for forwarding
$forwardData = [];
if (!empty($postData)) {
    $forwardData = $postData;
} elseif (!empty($rawInput)) {
    // Try to parse JSON if raw input is provided
    $jsonData = json_decode($rawInput, true);
    if ($jsonData) {
        $forwardData = $jsonData;
    } else {
        // Parse as query string
        parse_str($rawInput, $forwardData);
    }
}

// Forward the request to your actual notify URL
if (!empty($forwardData)) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $TARGET_URL);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($forwardData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Only for testing
    
    // Set headers to mimic original request
    $headers = [
        'Content-Type: application/x-www-form-urlencoded',
        'User-Agent: NewebPay-Webhook-Forwarder'
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    logMessage("Forwarded to $TARGET_URL - HTTP Code: $httpCode");
    if ($error) {
        logMessage("cURL Error: $error");
    }
    logMessage("Response: $response");
    
    // Return the response from the target
    http_response_code($httpCode);
    echo $response;
} else {
    logMessage("No data to forward");
    echo "No data received";
}
?>
