# Google Sheets 資料庫架構設計

## 主要工作表：donation_records

### 欄位設計 (A-T 欄)

| 欄位 | 欄位名稱 | 資料類型 | 說明 | 範例 |
|------|----------|----------|------|------|
| A | timestamp | 時間戳記 | 記錄建立時間 | 2024-01-15 14:30:25 |
| B | merchant_order_no | 文字 | 商店訂單編號 | CDL1705123456789 |
| C | amount | 數字 | 捐款金額 | 1000 |
| D | donor_name | 文字 | 捐款人姓名 | 王小明 |
| E | donor_email | 文字 | 捐款人信箱 | <EMAIL> |
| F | donor_phone | 文字 | 捐款人電話 | 0912345678 |
| G | payment_type | 文字 | 付款方式 | CREDIT |
| H | receipt_option | 文字 | 收據選項 | monthly_receipt |
| I | item_general | 數字 | 一般經費金額 | 500 |
| J | item_book | 數字 | 助印弘法金額 | 300 |
| K | item_life | 數字 | 護生關懷金額 | 200 |
| L | item_clinic | 數字 | 義診慈善金額 | 0 |
| M | item_building | 數字 | 建築修繕金額 | 0 |
| N | item_jewels | 數字 | 供養三寶金額 | 0 |
| O | status | 文字 | 付款狀態 | Pending/Completed/Failed |
| P | trade_info_sent | 文字 | 發送的加密資料 | (加密字串) |
| Q | trade_sha_sent | 文字 | 發送的雜湊值 | (雜湊字串) |
| R | newebpay_response | 文字 | 藍新回應資料 | (JSON字串) |
| S | updated_at | 時間戳記 | 最後更新時間 | 2024-01-15 14:35:10 |
| T | notes | 文字 | 備註 | 手動確認付款 |

### 輔助工作表

#### 1. payment_methods (付款方式對照表)
| 欄位 | 說明 |
|------|------|
| A | 付款代碼 (CREDIT, UNIONPAY, etc.) |
| B | 付款方式中文名稱 |
| C | 是否啟用 |

#### 2. donation_items (捐款項目對照表)
| 欄位 | 說明 |
|------|------|
| A | 項目代碼 |
| B | 項目中文名稱 |
| C | 項目說明 |
| D | 是否啟用 |

#### 3. daily_summary (每日統計)
| 欄位 | 說明 |
|------|------|
| A | 日期 |
| B | 總筆數 |
| C | 總金額 |
| D | 成功筆數 |
| E | 失敗筆數 |
| F | 待處理筆數 |

## Google Sheets 設定建議

### 1. 資料驗證
- 狀態欄位：下拉選單 (Pending, Completed, Failed, Cancelled)
- 付款方式：下拉選單 (從 payment_methods 工作表取得)
- 金額欄位：數字格式，最小值 0

### 2. 條件格式
- 狀態為 "Completed" 的列：綠色背景
- 狀態為 "Failed" 的列：紅色背景
- 狀態為 "Pending" 的列：黃色背景

### 3. 公式建議
- 總金額驗證：`=I2+J2+K2+L2+M2+N2` (應等於 C 欄)
- 自動時間戳記：使用 Google Apps Script 觸發器

### 4. 權限設定
- 編輯權限：僅限系統管理員
- 檢視權限：相關業務人員
- 共用連結：關閉

## 索引和查詢優化

### 常用查詢
1. 依訂單編號查詢：`=FILTER(A:T, B:B=訂單編號)`
2. 依日期範圍查詢：`=FILTER(A:T, (A:A>=開始日期)*(A:A<=結束日期))`
3. 依狀態查詢：`=FILTER(A:T, O:O="Completed")`
4. 統計查詢：`=SUMIF(O:O,"Completed",C:C)`

### 效能考量
- 建議每月建立新的工作表分頁
- 保留主工作表為當月資料
- 歷史資料移至對應月份分頁
- 使用 IMPORTRANGE 函數整合多月資料

## 備份策略
1. Google Sheets 自動版本控制
2. 定期匯出 CSV 檔案備份
3. 使用 Google Apps Script 自動備份到 Google Drive
