/**
 * 最小化 POST 修復版本 - Code.gs
 * 專門解決 POST 請求問題
 */

// === 系統設定 ===
const CONFIG = {
  SPREADSHEET_ID: '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc',
  SHEET_NAME: 'donation_records',
  LOG_SHEET_NAME: 'system_logs'
};

/**
 * 處理 GET 請求
 */
function doGet(e) {
  try {
    const action = e.parameter.action || 'form';
    
    // 記錄 GET 請求
    logToSheet('GET Request', { action: action });
    
    if (action === 'test') {
      return handleTest();
    } else if (action === 'posttest') {
      return handlePostTest();
    } else {
      return showMinimalForm();
    }
    
  } catch (error) {
    logToSheet('GET Error', error.toString());
    return createErrorResponse('GET 錯誤: ' + error.toString());
  }
}

/**
 * 處理 POST 請求
 */
function doPost(e) {
  try {
    // 立即記錄 POST 請求開始
    logToSheet('POST Started', 'POST request received');
    
    // 檢查是否有參數
    if (!e || !e.parameters) {
      logToSheet('POST Error', 'No parameters object');
      return createErrorResponse('沒有接收到表單資料');
    }
    
    // 記錄參數
    logToSheet('POST Parameters', Object.keys(e.parameters));
    
    // 提取參數
    const params = {};
    try {
      Object.keys(e.parameters).forEach(key => {
        params[key] = e.parameters[key][0];
      });
      logToSheet('Extracted Parameters', params);
    } catch (paramError) {
      logToSheet('Parameter Extraction Error', paramError.toString());
      return createErrorResponse('參數提取失敗');
    }
    
    // 基本驗證
    if (!params.donor_name) {
      logToSheet('Validation Error', 'Missing donor_name');
      return createErrorResponse('缺少捐款人姓名');
    }
    
    if (!params.donor_email) {
      logToSheet('Validation Error', 'Missing donor_email');
      return createErrorResponse('缺少電子郵件');
    }
    
    // 計算金額
    const amount1 = parseInt(params.item_amount_general_expenses) || 0;
    const amount2 = parseInt(params.item_amount_book_printing) || 0;
    const totalAmount = amount1 + amount2;
    
    logToSheet('Amount Calculation', { amount1, amount2, totalAmount });
    
    if (totalAmount <= 0) {
      logToSheet('Validation Error', 'Invalid amount: ' + totalAmount);
      return createErrorResponse('捐款金額必須大於零');
    }
    
    // 生成訂單編號
    const orderNo = 'MINIMAL' + Date.now();
    logToSheet('Order Generated', orderNo);
    
    // 嘗試儲存記錄
    try {
      const saveResult = saveRecord(params, orderNo, totalAmount);
      logToSheet('Save Result', saveResult);
      
      if (saveResult.success) {
        logToSheet('POST Success', 'All operations completed');
        return createSuccessResponse(orderNo, totalAmount);
      } else {
        return createErrorResponse('儲存失敗: ' + saveResult.error);
      }
      
    } catch (saveError) {
      logToSheet('Save Exception', saveError.toString());
      return createErrorResponse('儲存異常: ' + saveError.toString());
    }
    
  } catch (error) {
    logToSheet('POST Exception', error.toString());
    return createErrorResponse('POST 處理異常: ' + error.toString());
  }
}

/**
 * 顯示最小化表單
 */
function showMinimalForm() {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <meta charset="UTF-8">
      <title>最小化測試表單</title>
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; }
        .submit-btn { background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; width: 100%; }
        .submit-btn:disabled { background: #6c757d; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 10px 0; display: none; }
      </style>
    </head>
    <body>
      <div class="container">
        <h2>🔧 最小化 POST 測試表單</h2>
        
        <div class="info">
          <strong>測試狀態：</strong>專門解決 POST 問題<br>
          <strong>時間：</strong>${new Date().toLocaleString('zh-TW')}<br>
          <strong>版本：</strong>最小化修復版
        </div>
        
        <div id="statusDiv" class="status"></div>
        
        <form id="testForm" method="post">
          <div class="form-group">
            <label for="donor_name">捐款人姓名 *</label>
            <input type="text" id="donor_name" name="donor_name" required>
          </div>
          
          <div class="form-group">
            <label for="donor_email">電子郵件 *</label>
            <input type="email" id="donor_email" name="donor_email" required>
          </div>
          
          <div class="form-group">
            <label for="amount1">一般經費</label>
            <input type="number" id="amount1" name="item_amount_general_expenses" min="0" value="1000">
          </div>
          
          <div class="form-group">
            <label for="amount2">助印弘法</label>
            <input type="number" id="amount2" name="item_amount_book_printing" min="0" value="500">
          </div>
          
          <button type="submit" class="submit-btn" id="submitBtn">提交測試</button>
        </form>
        
        <div class="info">
          <strong>測試連結：</strong><br>
          <a href="?test=true" target="_blank">系統測試</a> |
          <a href="?posttest=true" target="_blank">POST 測試</a>
        </div>
      </div>
      
      <script>
        function showStatus(message) {
          const statusDiv = document.getElementById('statusDiv');
          statusDiv.textContent = message;
          statusDiv.style.display = 'block';
        }
        
        document.getElementById('testForm').addEventListener('submit', function(e) {
          console.log('Form submit started');
          
          const submitBtn = document.getElementById('submitBtn');
          submitBtn.textContent = '處理中...';
          submitBtn.disabled = true;
          
          showStatus('正在提交表單，請稍候...');
          
          // 延長超時時間
          setTimeout(function() {
            if (submitBtn.disabled) {
              showStatus('提交超時 - 請檢查 system_logs 工作表');
              submitBtn.textContent = '提交測試';
              submitBtn.disabled = false;
            }
          }, 60000); // 60秒超時
          
          console.log('Form submission allowed');
          return true;
        });
        
        console.log('Minimal form loaded');
      </script>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html)
    .setTitle('最小化測試表單')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * POST 測試處理
 */
function handlePostTest() {
  try {
    logToSheet('POST Test Started', 'Manual POST test');
    
    // 模擬 POST 資料
    const mockEvent = {
      parameters: {
        donor_name: ['POST測試用戶'],
        donor_email: ['<EMAIL>'],
        item_amount_general_expenses: ['2000'],
        item_amount_book_printing: ['1000']
      }
    };
    
    logToSheet('POST Test Event', 'Calling doPost with mock data');
    
    return doPost(mockEvent);
    
  } catch (error) {
    logToSheet('POST Test Error', error.toString());
    return createErrorResponse('POST 測試錯誤: ' + error.toString());
  }
}

/**
 * 儲存記錄
 */
function saveRecord(params, orderNo, amount) {
  try {
    logToSheet('Save Record Start', { orderNo, amount });
    
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    logToSheet('Spreadsheet Opened', 'Success');
    
    const sheet = spreadsheet.getSheetByName(CONFIG.SHEET_NAME);
    if (!sheet) {
      throw new Error('Sheet not found: ' + CONFIG.SHEET_NAME);
    }
    logToSheet('Sheet Found', CONFIG.SHEET_NAME);
    
    const record = [
      new Date().toLocaleString('zh-TW'),  // timestamp
      orderNo,                             // merchant_order_no
      amount,                              // amount
      params.donor_name || '',             // donor_name
      params.donor_email || '',            // donor_email
      '',                                  // donor_phone
      'MINIMAL_TEST',                      // payment_type
      'test',                              // receipt_option
      parseInt(params.item_amount_general_expenses) || 0,  // item_general
      parseInt(params.item_amount_book_printing) || 0,     // item_book
      0, 0, 0, 0,                         // 其他項目
      'Minimal Test',                      // status
      '', '', '',                         // 藍新相關欄位
      new Date().toLocaleString('zh-TW'),  // updated_at
      'Minimal POST test record'           // notes
    ];
    
    logToSheet('Record Prepared', record.length + ' fields');
    
    sheet.appendRow(record);
    logToSheet('Record Appended', 'Success');
    
    return { success: true, message: 'Record saved successfully' };
    
  } catch (error) {
    logToSheet('Save Record Error', error.toString());
    return { success: false, error: error.toString() };
  }
}

/**
 * 記錄到工作表
 */
function logToSheet(type, data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const logSheet = spreadsheet.getSheetByName(CONFIG.LOG_SHEET_NAME);
    
    logSheet.appendRow([
      new Date(),
      'MINIMAL_POST: ' + type,
      typeof data === 'object' ? JSON.stringify(data) : data,
      'Minimal POST Session'
    ]);
    
  } catch (error) {
    console.error('Logging failed:', error);
  }
}

/**
 * 系統測試
 */
function handleTest() {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    
    const result = {
      status: 'MINIMAL_POST_OK',
      timestamp: new Date().toISOString(),
      spreadsheet_name: spreadsheet.getName(),
      sheets: spreadsheet.getSheets().map(sheet => sheet.getName()),
      message: '最小化 POST 測試成功'
    };
    
    logToSheet('System Test', result);
    
    return ContentService
      .createTextOutput(JSON.stringify(result, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    const errorResult = {
      status: 'MINIMAL_POST_ERROR',
      error: error.toString()
    };
    
    return ContentService
      .createTextOutput(JSON.stringify(errorResult, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 建立成功回應
 */
function createSuccessResponse(orderNo, amount) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>最小化測試成功</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; text-align: center; }
        .container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        .success { color: #28a745; font-size: 24px; margin: 20px 0; }
        .info { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .button { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="success">✅ 最小化 POST 測試成功！</div>
        
        <div class="info">
          <h3>處理結果</h3>
          <p><strong>訂單編號：</strong>${orderNo}</p>
          <p><strong>金額：</strong>NT$ ${amount}</p>
          <p><strong>時間：</strong>${new Date().toLocaleString('zh-TW')}</p>
        </div>
        
        <div class="info">
          <h3>✅ 系統狀態正常</h3>
          <p>POST 請求處理成功</p>
          <p>資料庫寫入成功</p>
          <p>請檢查 donation_records 工作表</p>
        </div>
        
        <a href="?" class="button">返回表單</a>
        <a href="https://docs.google.com/spreadsheets/d/${CONFIG.SPREADSHEET_ID}/edit" class="button" target="_blank">查看 Google Sheets</a>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}

/**
 * 建立錯誤回應
 */
function createErrorResponse(message) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>最小化測試錯誤</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; text-align: center; }
        .container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        .error { color: #dc3545; font-size: 18px; margin: 20px 0; }
        .info { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .button { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="error">❌ 最小化測試錯誤</div>
        
        <div class="info">
          <h3>錯誤訊息</h3>
          <p>${message}</p>
          <p><strong>時間：</strong>${new Date().toLocaleString('zh-TW')}</p>
        </div>
        
        <a href="?" class="button">返回表單</a>
        <a href="?posttest=true" class="button">POST 測試</a>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}
