/**
 * 簡化版測試程式碼 - Code.gs
 * 先確保基本功能正常運作
 */

// === 設定 ===
const CONFIG = {
  SPREADSHEET_ID: '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc'
};

/**
 * 處理 GET 請求
 */
function doGet(e) {
  try {
    const action = e.parameter.action || 'form';
    
    // 記錄請求
    console.log('GET Request:', action);
    
    switch (action) {
      case 'test':
        return handleTest();
      case 'form':
        return showForm();
      default:
        return showForm();
    }
    
  } catch (error) {
    console.error('GET Error:', error);
    return createTextResponse('錯誤: ' + error.toString());
  }
}

/**
 * 處理 POST 請求
 */
function doPost(e) {
  try {
    console.log('POST Request received');
    return createTextResponse('POST 請求已收到');
    
  } catch (error) {
    console.error('POST Error:', error);
    return createTextResponse('POST 錯誤: ' + error.toString());
  }
}

/**
 * 處理測試請求
 */
function handleTest() {
  try {
    // 測試 Spreadsheet 存取
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const spreadsheetName = spreadsheet.getName();
    
    // 測試結果
    const result = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      spreadsheet_access: true,
      spreadsheet_name: spreadsheetName,
      sheets_count: spreadsheet.getSheets().length,
      message: '系統測試成功'
    };
    
    console.log('Test result:', result);
    
    return ContentService
      .createTextOutput(JSON.stringify(result, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('Test error:', error);
    
    const errorResult = {
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      spreadsheet_access: false,
      error: error.toString(),
      message: '系統測試失敗'
    };
    
    return ContentService
      .createTextOutput(JSON.stringify(errorResult, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 顯示簡單表單
 */
function showForm() {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>慈光圖書館 - 線上捐款系統測試</title>
      <style>
        body {
          font-family: 'Microsoft JhengHei', Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background: #f5f5f5;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background: white;
          padding: 30px;
          border-radius: 10px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 2px solid #3498db;
        }
        .status {
          background: #d4edda;
          border: 1px solid #c3e6cb;
          color: #155724;
          padding: 15px;
          border-radius: 5px;
          margin-bottom: 20px;
        }
        .test-links {
          margin: 20px 0;
        }
        .test-links a {
          display: inline-block;
          background: #3498db;
          color: white;
          padding: 10px 20px;
          text-decoration: none;
          border-radius: 5px;
          margin: 5px;
        }
        .test-links a:hover {
          background: #2980b9;
        }
        .info {
          background: #d1ecf1;
          border: 1px solid #bee5eb;
          color: #0c5460;
          padding: 15px;
          border-radius: 5px;
          margin: 20px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🏛️ 慈光圖書館捐款系統</h1>
          <p>系統測試頁面</p>
        </div>
        
        <div class="status">
          ✅ <strong>系統狀態：正常運作</strong><br>
          Web App 已成功部署並可以接收請求
        </div>
        
        <div class="info">
          <h3>📊 系統資訊</h3>
          <p><strong>部署時間：</strong>${new Date().toLocaleString('zh-TW')}</p>
          <p><strong>Sheets ID：</strong>${CONFIG.SPREADSHEET_ID}</p>
          <p><strong>狀態：</strong>基本功能測試版本</p>
        </div>
        
        <div class="test-links">
          <h3>🧪 測試功能</h3>
          <a href="?test=true" target="_blank">系統狀態檢查</a>
          <a href="https://docs.google.com/spreadsheets/d/${CONFIG.SPREADSHEET_ID}/edit" target="_blank">開啟 Google Sheets</a>
        </div>
        
        <div class="info">
          <h3>📋 下一步</h3>
          <ol>
            <li>點擊「系統狀態檢查」確認基本功能</li>
            <li>檢查 Google Sheets 是否可以正常存取</li>
            <li>確認所有工作表已建立完成</li>
            <li>準備部署完整版本</li>
          </ol>
        </div>
        
        <div class="info">
          <h3>🔧 技術資訊</h3>
          <p><strong>Web App URL：</strong></p>
          <code>https://script.google.com/macros/s/AKfycbwTKT0l7fUwn0p4prFAjH1m7a1O4RVsz7Up-Du6uJSjN1tkxqZT8S1U8XtT4Hkcb-IK/exec</code>
          
          <p><strong>測試 URL：</strong></p>
          <code>https://script.google.com/macros/s/AKfycbwTKT0l7fUwn0p4prFAjH1m7a1O4RVsz7Up-Du6uJSjN1tkxqZT8S1U8XtT4Hkcb-IK/exec?test=true</code>
        </div>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html)
    .setTitle('慈光圖書館捐款系統測試')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * 建立文字回應
 */
function createTextResponse(message) {
  return ContentService.createTextOutput(message);
}

/**
 * 測試 Spreadsheet 連接
 */
function testSpreadsheetConnection() {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheets = spreadsheet.getSheets();
    
    console.log('Spreadsheet name:', spreadsheet.getName());
    console.log('Number of sheets:', sheets.length);
    
    sheets.forEach(sheet => {
      console.log('Sheet name:', sheet.getName());
    });
    
    return {
      success: true,
      name: spreadsheet.getName(),
      sheetsCount: sheets.length,
      sheetNames: sheets.map(sheet => sheet.getName())
    };
    
  } catch (error) {
    console.error('Spreadsheet connection error:', error);
    return {
      success: false,
      error: error.toString()
    };
  }
}
