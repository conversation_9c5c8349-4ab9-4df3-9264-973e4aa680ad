<!DOCTYPE html>
<html>
<head>
    <title>慈光圖書館線上捐款</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="main-container">
        <div class="form-container">
            <h2>線上捐款</h2>
            <!-- 表單 action 指向新的處理腳本 -->
            <form method="post" action="process_donation.php">

                <div class="form-section">
                    <label>護持項目:</label>
                    <div class="checkbox-list">
                        <!-- 注意：這裡的 name 屬性是 item_amount_xxx， process_donation.php 將以此名稱接收金額 -->
                        <div><label for="item1">【一般經費】</label> <input type="number" id="item1" name="item_amount_general_expenses" placeholder="金額" value="0" min="0"></div>
                        <div><label for="item2">【助印弘法】</label> <input type="number" id="item2" name="item_amount_book_printing" placeholder="金額" value="0" min="0"></div>
                        <div><label for="item3">【護生關懷助念】</label> <input type="number" id="item3" name="item_amount_life_release" placeholder="金額" value="0" min="0"></div>
                        <div><label for="item4">【義診慈善救濟】</label> <input type="number" id="item4" name="item_amount_free_clinic" placeholder="金額" value="0" min="0"></div>
                        <div><label for="item5">【建築修繕設備】</label> <input type="number" id="item5" name="item_amount_building_repair" placeholder="金額" value="0" min="0"></div>
                        <div><label for="item6">【供養三寶】</label> <input type="number" id="item6" name="item_amount_offering_three_jewels" placeholder="金額" value="0" min="0"></div>
                        <div class="total-amount">
                            <label>合計金額:</label>
                            <div id="total-amount-display">0 元</div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <label>付款方式:</label>
                    <div class="radio-list">
                         <!-- 這裡的 name 是 payment_type， process_donation.php 將以此名稱接收選擇的付款方式 -->
                        <div><input type="radio" id="payment1" name="payment_type" value="CREDIT" required><label for="payment1">信用卡</label></div>
                        <div><input type="radio" id="payment2" name="payment_type" value="UNIONPAY"><label for="payment2">銀聯卡</label></div>
                        <div><input type="radio" id="payment3" name="payment_type" value="CVS"><label for="payment3">超商條碼繳費</label></div>
                        <div><input type="radio" id="payment4" name="payment_type" value="TAIWANPAY"><label for="payment4">台灣 Pay</label></div>
                        <div><input type="radio" id="payment5" name="payment_type" value="LINEPAY"><label for="payment5">LINE Pay</label></div>
                        <div><input type="radio" id="payment6" name="payment_type" value="GOOGLEPAY"><label for="payment6">Google Pay</label></div>
                        <div><input type="radio" id="payment7" name="payment_type" value="WEBATM"><label for="payment7">WebATM</label></div>
                        <div><input type="radio" id="payment8" name="payment_type" value="VACC"><label for="payment8">ATM 轉帳</label></div>
                        <div><input type="radio" id="payment9" name="payment_type" value="FOREIGN"><label for="payment9">國外卡</label></div>
                         <!-- 您可以根據藍新金流技術手冊，在這個表單中加入其他需要傳遞給 NewebPay 但不需要加密的參數的隱藏欄位，例如 RespondType, Version 等。不過更常見的做法是在 process_donation.php 中固定這些值。目前的 donate.php 是在 PHP 中構造這些參數的。 -->
                    </div>
                </div>

                <div class="form-section">
                    <label for="donor_name">聯絡姓名:</label>
                    <input type="text" id="donor_name" name="donor_name" required>
                </div>

                <div class="form-section">
                    <label for="donor_email">電子信箱:</label>
                    <input type="email" id="donor_email" name="donor_email" required>
                </div>

                <div class="form-section">
                    <label for="donor_phone">手機或電話號碼:</label>
                    <input type="text" id="donor_phone" name="donor_phone" required>
                </div>

                <div class="form-section">
                    <label>捐款收據:</label>
                    <div class="radio-list">
                        <!-- 這裡的 name 是 receipt_option， process_donation.php 將以此名稱接收選擇的收據選項 -->
                        <div><input type="radio" id="receipt1" name="receipt_option" value="no_receipt" checked><label for="receipt1">開立不郵寄</label></div>
                        <div><input type="radio" id="receipt2" name="receipt_option" value="monthly_receipt"><label for="receipt2">依次寄發收據</label></div>
                        <div><input type="radio" id="receipt3" name="receipt_option" value="annual_receipt"><label for="receipt3">開立年度收據(隔年三月陸續寄發)</label></div>
                    </div>
                </div>

                <div class="form-section agreement">
                    <div class="declaration-text">
                        財團法人慈光圖書館  捐款徵信聲明
                        感謝您護持「財團法人慈光圖書館」弘法利生事業，您的捐款將用於... (此處請填寫捐款徵信聲明內容) ...
                        本館將依法開立捐款收據，您的捐款資料將受到妥善保護，僅供本館內部作業及寄發收據使用，絕不對外洩漏。
                    </div>
                    <div class="checkbox-container">
                        <input type="checkbox" id="agreement" name="agreement" required>
                        <label for="agreement">我同意上述聲明事項 <span class="required-indicator">(必填)</span></label>
                    </div>
                </div>

                <div class="form-actions">
                    <input type="submit" value="確定送出">
                    <button type="reset" class="reset-button">重新填寫</button>
                </div>
            </form>
        </div>
    </div>
    <!-- script.js 用於前端互動，例如總金額計算 -->
    <script src="script.js"></script>
    <script>
        // 添加 JavaScript 來計算總金額並更新顯示
        const itemAmountInputs = document.querySelectorAll('.checkbox-list input[type="number"]');
        const totalAmountDisplay = document.getElementById('total-amount-display');

        function updateTotalAmount() {
            let total = 0;
            itemAmountInputs.forEach(input => {
                const amount = parseInt(input.value) || 0; // 確保是數字，非數字則為 0
                total += amount;
            });
            totalAmountDisplay.textContent = total + ' 元';
        }

        // 在數字輸入框值改變時更新總金額
        itemAmountInputs.forEach(input => {
            input.addEventListener('input', updateTotalAmount);
        });

        // 頁面載入時計算一次初始總金額
        updateTotalAmount();

    </script>
</body>
</html>