/**
 * Google Apps Script - 主控制器
 * 這是整個捐款系統的主要入口點
 * 檔案名稱：Code.gs
 */

// === 全域設定 ===
const SYSTEM_CONFIG = {
  SPREADSHEET_ID: '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc',
  SHEET_NAME: 'donation_records',
  LOG_SHEET_NAME: 'system_logs',
  
  // 藍新金流設定
  NEWEBPAY: {
    KEY: 'vTxBcUTfd54kxzh01qborPcL5kJtMoxE',
    IV: 'PEEDYyAVozYO6rDC',
    MERCHANT_ID: 'MS3757703701',
    VERSION: '2.0',
    GATEWAY_URL: 'https://core.newebpay.com/MPG/mpg_gateway'
  }
};

/**
 * Web App 主要入口點 - 處理 GET 請求
 */
function doGet(e) {
  try {
    const action = e.parameter.action || 'form';
    
    logSystemOperation('GET Request', { action: action, parameters: e.parameters });
    
    switch (action) {
      case 'form':
        return showDonationForm();
      case 'return':
        return handlePaymentReturn(e);
      case 'test':
        return handleSystemTest();
      case 'status':
        return handleStatusCheck(e);
      default:
        return showDonationForm();
    }
    
  } catch (error) {
    logSystemOperation('GET Error', error.toString());
    return createErrorPage('系統錯誤，請稍後再試');
  }
}

/**
 * Web App 主要入口點 - 處理 POST 請求
 */
function doPost(e) {
  try {
    const action = e.parameter.action || 'donate';
    
    logSystemOperation('POST Request', { action: action });
    
    switch (action) {
      case 'donate':
        return handleDonationSubmission(e);
      case 'notify':
        return handlePaymentNotification(e);
      case 'test_notify':
        return handleTestNotification(e);
      default:
        return createErrorPage('未知的操作');
    }
    
  } catch (error) {
    logSystemOperation('POST Error', error.toString());
    return createErrorPage('處理請求時發生錯誤');
  }
}

/**
 * 顯示捐款表單
 */
function showDonationForm() {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>慈光圖書館 - 線上捐款</title>
      <style>
        body { font-family: 'Microsoft JhengHei', Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header p { color: #7f8c8d; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #34495e; }
        .form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        .donation-items { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .donation-item { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .donation-item label { margin-bottom: 0; flex: 1; }
        .donation-item input { width: 120px; margin-left: 10px; }
        .total-amount { background: #e8f5e8; padding: 15px; border-radius: 5px; text-align: center; font-size: 18px; font-weight: bold; color: #27ae60; }
        .submit-btn { background: #3498db; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 18px; cursor: pointer; width: 100%; }
        .submit-btn:hover { background: #2980b9; }
        .required { color: #e74c3c; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>慈光圖書館線上捐款</h1>
          <p>感謝您的護持，您的每一分善款都將用於弘法利生</p>
        </div>
        
        <form id="donationForm" method="post">
          <input type="hidden" name="action" value="donate">
          
          <div class="form-group">
            <label for="donor_name">捐款人姓名 <span class="required">*</span></label>
            <input type="text" id="donor_name" name="donor_name" required>
          </div>
          
          <div class="form-group">
            <label for="donor_email">電子郵件 <span class="required">*</span></label>
            <input type="email" id="donor_email" name="donor_email" required>
          </div>
          
          <div class="form-group">
            <label for="donor_phone">聯絡電話</label>
            <input type="tel" id="donor_phone" name="donor_phone">
          </div>
          
          <div class="donation-items">
            <h3>護持項目</h3>
            <div class="donation-item">
              <label for="item_general">一般經費</label>
              <input type="number" id="item_general" name="item_amount_general_expenses" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_book">助印弘法</label>
              <input type="number" id="item_book" name="item_amount_book_printing" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_life">護生關懷助念</label>
              <input type="number" id="item_life" name="item_amount_life_release" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_clinic">義診慈善救濟</label>
              <input type="number" id="item_clinic" name="item_amount_free_clinic" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_building">建築修繕設備</label>
              <input type="number" id="item_building" name="item_amount_building_repair" min="0" value="0" onchange="calculateTotal()">
            </div>
            <div class="donation-item">
              <label for="item_jewels">供養三寶</label>
              <input type="number" id="item_jewels" name="item_amount_offering_three_jewels" min="0" value="0" onchange="calculateTotal()">
            </div>
          </div>
          
          <div class="total-amount">
            總捐款金額：NT$ <span id="totalAmount">0</span>
          </div>
          
          <div class="form-group">
            <label for="receipt_option">收據選項</label>
            <select id="receipt_option" name="receipt_option">
              <option value="no_receipt">不需要收據</option>
              <option value="monthly_receipt">依次寄發收據</option>
              <option value="annual_receipt">年度收據</option>
            </select>
          </div>
          
          <button type="submit" class="submit-btn">確認捐款</button>
        </form>
      </div>
      
      <script>
        function calculateTotal() {
          const items = [
            'item_amount_general_expenses',
            'item_amount_book_printing', 
            'item_amount_life_release',
            'item_amount_free_clinic',
            'item_amount_building_repair',
            'item_amount_offering_three_jewels'
          ];
          
          let total = 0;
          items.forEach(item => {
            const value = parseInt(document.getElementsByName(item)[0].value) || 0;
            total += value;
          });
          
          document.getElementById('totalAmount').textContent = total.toLocaleString();
        }
        
        document.getElementById('donationForm').addEventListener('submit', function(e) {
          const total = parseInt(document.getElementById('totalAmount').textContent.replace(/,/g, ''));
          if (total <= 0) {
            e.preventDefault();
            alert('請輸入捐款金額');
            return false;
          }
          
          // 顯示處理中訊息
          const submitBtn = document.querySelector('.submit-btn');
          submitBtn.textContent = '處理中...';
          submitBtn.disabled = true;
        });
        
        // 初始計算
        calculateTotal();
      </script>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html)
    .setTitle('慈光圖書館線上捐款')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * 處理捐款表單提交
 */
function handleDonationSubmission(e) {
  try {
    // 解析表單資料
    const formData = {};
    Object.keys(e.parameters).forEach(key => {
      formData[key] = e.parameters[key][0]; // parameters 是陣列格式
    });
    
    logSystemOperation('Donation Submission', { 
      donor_name: formData.donor_name,
      donor_email: formData.donor_email 
    });
    
    // 使用付款處理器處理捐款
    return processDonation(formData);
    
  } catch (error) {
    logSystemOperation('Donation Submission Error', error.toString());
    return createErrorPage('處理捐款時發生錯誤');
  }
}

/**
 * 處理付款通知
 */
function handlePaymentNotification(e) {
  try {
    logSystemOperation('Payment Notification', { parameters: e.parameters });
    
    // 使用通知處理器處理通知
    return doPost(e); // 調用 notify handler 的 doPost
    
  } catch (error) {
    logSystemOperation('Payment Notification Error', error.toString());
    return ContentService.createTextOutput('ERROR');
  }
}

/**
 * 處理付款返回
 */
function handlePaymentReturn(e) {
  try {
    // 這裡可以顯示付款結果頁面
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>付款結果</title>
        <meta charset="utf-8">
      </head>
      <body>
        <h1>付款處理完成</h1>
        <p>感謝您的捐款，我們將盡快處理您的付款結果。</p>
        <p>如有疑問，請聯繫我們。</p>
      </body>
      </html>
    `;
    
    return HtmlService.createHtmlOutput(html);
    
  } catch (error) {
    logSystemOperation('Payment Return Error', error.toString());
    return createErrorPage('處理付款結果時發生錯誤');
  }
}

/**
 * 系統測試
 */
function handleSystemTest() {
  const testResults = {
    timestamp: new Date().toISOString(),
    spreadsheet_access: false,
    sheets_write: false,
    crypto_functions: false
  };
  
  try {
    // 測試 Spreadsheet 存取
    const spreadsheet = SpreadsheetApp.openById(SYSTEM_CONFIG.SPREADSHEET_ID);
    testResults.spreadsheet_access = true;
    
    // 測試寫入功能
    const sheet = spreadsheet.getSheetByName(SYSTEM_CONFIG.SHEET_NAME);
    if (sheet) {
      testResults.sheets_write = true;
    }
    
    // 測試加密功能
    const testString = 'test';
    const hash = Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, testString);
    if (hash) {
      testResults.crypto_functions = true;
    }
    
  } catch (error) {
    testResults.error = error.toString();
  }
  
  return ContentService
    .createTextOutput(JSON.stringify(testResults, null, 2))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * 建立錯誤頁面
 */
function createErrorPage(message) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>錯誤</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .error { color: #dc3545; font-size: 18px; margin: 20px 0; }
        .button { display: inline-block; padding: 10px 20px; background: #007bff; 
                  color: white; text-decoration: none; border-radius: 5px; }
      </style>
    </head>
    <body>
      <h2>系統錯誤</h2>
      <p class="error">${message}</p>
      <a href="?" class="button">返回首頁</a>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}

/**
 * 記錄系統操作
 */
function logSystemOperation(type, data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(SYSTEM_CONFIG.SPREADSHEET_ID);
    let logSheet = spreadsheet.getSheetByName(SYSTEM_CONFIG.LOG_SHEET_NAME);
    
    if (!logSheet) {
      logSheet = spreadsheet.insertSheet(SYSTEM_CONFIG.LOG_SHEET_NAME);
      logSheet.getRange(1, 1, 1, 4).setValues([
        ['Timestamp', 'Type', 'Data', 'User']
      ]);
    }
    
    logSheet.appendRow([
      new Date(),
      type,
      typeof data === 'object' ? JSON.stringify(data) : data,
      Session.getActiveUser().getEmail()
    ]);
    
  } catch (error) {
    console.error('Logging failed:', error);
  }
}
