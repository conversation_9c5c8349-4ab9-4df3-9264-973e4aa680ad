<?php
// poll_payment_status.php - Poll NewebPay for payment status when notifications fail

// --- NewebPay API Configuration ---
$key = "vTxBcUTfd54kxzh01qborPcL5kJtMoxE";
$iv = "PEEDYyAVozYO6rDC";
$mid = "MS3757703701";

// --- Database Connection ---
$servername = "sql301.infinityfree.com";
$username = "if0_37792451";
$password = "tcbdct12";
$dbname = "if0_37792451_donate";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Database connection failed: " . $conn->connect_error);
}

// Function to query NewebPay for transaction status
function queryPaymentStatus($merchantOrderNo, $amount, $key, $iv, $mid) {
    // Prepare data for query
    $data = array(
        'MerchantID' => $mid,
        'Version' => '1.3',
        'RespondType' => 'JSON',
        'TimeStamp' => time(),
        'MerchantOrderNo' => $merchantOrderNo,
        'Amt' => $amount
    );
    
    // Build query string
    $queryString = http_build_query($data);
    
    // Encrypt data
    $checkValue = bin2hex(openssl_encrypt($queryString, "AES-256-CBC", $key, OPENSSL_RAW_DATA, $iv));
    
    // Create hash
    $checkCode = strtoupper(hash("sha256", "HashKey=" . $key . "&" . $checkValue . "&HashIV=" . $iv));
    
    // Prepare POST data for query API
    $postData = array(
        'MerchantID' => $mid,
        'Version' => '1.3',
        'CheckValue' => $checkValue,
        'CheckCode' => $checkCode
    );
    
    // Send request to NewebPay query API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://core.newebpay.com/API/QueryTradeInfo');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200 && $response) {
        return json_decode($response, true);
    }
    
    return false;
}

// Get pending transactions (older than 5 minutes)
$fiveMinutesAgo = date('Y-m-d H:i:s', strtotime('-5 minutes'));
$sql = "SELECT merchant_order_no, amount, donor_name, donor_email FROM donation_records 
        WHERE status = 'Pending' AND created_at < ? 
        ORDER BY created_at ASC LIMIT 10";

$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $fiveMinutesAgo);
$stmt->execute();
$result = $stmt->get_result();

$logFile = 'poll_status_log.txt';
$logMessage = "=== Payment Status Poll - " . date('Y-m-d H:i:s') . " ===\n";

while ($row = $result->fetch_assoc()) {
    $merchantOrderNo = $row['merchant_order_no'];
    $amount = $row['amount'];
    
    $logMessage .= "Checking order: $merchantOrderNo\n";
    
    // Query payment status
    $statusResult = queryPaymentStatus($merchantOrderNo, $amount, $key, $iv, $mid);
    
    if ($statusResult) {
        $logMessage .= "Query result: " . print_r($statusResult, true) . "\n";
        
        // Parse the result and update database accordingly
        if (isset($statusResult['Status']) && $statusResult['Status'] === 'SUCCESS') {
            // Update status to completed
            $updateSql = "UPDATE donation_records SET status = 'Completed', 
                         updated_at = NOW() WHERE merchant_order_no = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param("s", $merchantOrderNo);
            $updateStmt->execute();
            
            $logMessage .= "Updated order $merchantOrderNo to Completed\n";
            
            // You can add email notification logic here
            
        } elseif (isset($statusResult['Status']) && $statusResult['Status'] === 'FAILED') {
            // Update status to failed
            $updateSql = "UPDATE donation_records SET status = 'Failed', 
                         updated_at = NOW() WHERE merchant_order_no = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param("s", $merchantOrderNo);
            $updateStmt->execute();
            
            $logMessage .= "Updated order $merchantOrderNo to Failed\n";
        }
    } else {
        $logMessage .= "Failed to query status for order: $merchantOrderNo\n";
    }
    
    // Add delay to avoid overwhelming the API
    sleep(1);
}

$logMessage .= "=== End Poll ===\n\n";
file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

$stmt->close();
$conn->close();

echo "Payment status polling completed. Check poll_status_log.txt for details.";
?>
