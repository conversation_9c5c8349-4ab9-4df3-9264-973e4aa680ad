/**
 * 付款處理相關函數 - 添加到 Code.gs 的底部
 */

/**
 * 準備藍新金流資料
 */
function prepareNewebPayData(formData, merchantOrderNo, amount) {
  const currentUrl = ScriptApp.getService().getUrl();
  
  return {
    'MerchantID': DONATION_CONFIG.NEWEBPAY.MERCHANT_ID,
    'RespondType': 'String',
    'TimeStamp': Math.floor(Date.now() / 1000),
    'Version': DONATION_CONFIG.NEWEBPAY.VERSION,
    'MerchantOrderNo': merchantOrderNo,
    'Amt': amount,
    'ItemDesc': '慈光圖書館捐款',
    'NotifyURL': currentUrl + '?action=notify',
    'ReturnURL': currentUrl + '?action=return',
    'Email': formData.donor_email,
    'EmailModify': 1,
    'LoginType': 0,
    'CREDIT': 1,
    'UNIONPAY': 1,
    'CVS': 1,
    'TAIWANPAY': 1,
    'LINEPAY': 1,
    'GOOGLEPAY': 1,
    'WEBATM': 1,
    'VACC': 1,
    'FOREIGN': 1
  };
}

/**
 * 加密付款資料
 */
function encryptPaymentData(data) {
  // 建立查詢字串
  const queryString = Object.keys(data)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
    .join('&');
  
  // 簡化版加密 - 實際部署時需要真正的 AES 加密
  // 這裡使用 Base64 編碼作為示例
  const tradeInfo = Utilities.base64Encode(queryString);
  
  // 計算 SHA-256 雜湊
  const hashString = `HashKey=${DONATION_CONFIG.NEWEBPAY.KEY}&${tradeInfo}&HashIV=${DONATION_CONFIG.NEWEBPAY.IV}`;
  const tradeSha = Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, hashString)
    .map(byte => (byte < 0 ? byte + 256 : byte).toString(16).padStart(2, '0'))
    .join('').toUpperCase();
  
  return {
    tradeInfo: tradeInfo,
    tradeSha: tradeSha,
    merchantId: DONATION_CONFIG.NEWEBPAY.MERCHANT_ID,
    version: DONATION_CONFIG.NEWEBPAY.VERSION,
    originalData: queryString
  };
}

/**
 * 建立付款表單
 */
function createPaymentForm(encryptedData) {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <title>導向支付頁面</title>
      <meta charset="utf-8">
      <style>
        body {
          font-family: 'Microsoft JhengHei', Arial, sans-serif;
          text-align: center;
          padding: 50px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          margin: 0;
        }
        .container {
          background: white;
          padding: 40px;
          border-radius: 15px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          max-width: 500px;
          margin: 0 auto;
        }
        .loading {
          font-size: 20px;
          color: #2c3e50;
          margin: 20px 0;
        }
        .spinner {
          border: 4px solid #f3f3f3;
          border-top: 4px solid #3498db;
          border-radius: 50%;
          width: 50px;
          height: 50px;
          animation: spin 1s linear infinite;
          margin: 20px auto;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .info {
          background: #d1ecf1;
          border: 1px solid #bee5eb;
          color: #0c5460;
          padding: 15px;
          border-radius: 8px;
          margin: 20px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h2>🏛️ 慈光圖書館</h2>
        <div class="spinner"></div>
        <p class="loading">正在安全導向支付頁面，請稍候...</p>
        
        <div class="info">
          <p><strong>📋 注意事項：</strong></p>
          <ul style="text-align: left;">
            <li>請勿關閉瀏覽器或重新整理頁面</li>
            <li>系統將自動導向藍新金流安全付款頁面</li>
            <li>支援多種付款方式供您選擇</li>
          </ul>
        </div>
        
        <form id="payment-form" method="post" action="${DONATION_CONFIG.NEWEBPAY.GATEWAY_URL}">
          <input type="hidden" name="MerchantID" value="${encryptedData.merchantId}">
          <input type="hidden" name="Version" value="${encryptedData.version}">
          <input type="hidden" name="TradeInfo" value="${encryptedData.tradeInfo}">
          <input type="hidden" name="TradeSha" value="${encryptedData.tradeSha}">
        </form>
        
        <script>
          // 3秒後自動提交表單
          setTimeout(function() {
            document.getElementById("payment-form").submit();
          }, 3000);
          
          // 顯示倒數計時
          let countdown = 3;
          const countdownInterval = setInterval(function() {
            countdown--;
            if (countdown > 0) {
              document.querySelector('.loading').textContent = 
                \`正在安全導向支付頁面，請稍候... (\${countdown}秒)\`;
            } else {
              clearInterval(countdownInterval);
            }
          }, 1000);
        </script>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html)
    .setTitle('導向支付頁面')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * 處理付款通知
 */
function handlePaymentNotification(e) {
  try {
    logOperation('Payment Notification Received', e.parameters);
    
    // 簡化版通知處理
    // 實際部署時需要解密和驗證資料
    
    return ContentService.createTextOutput('OK');
    
  } catch (error) {
    logOperation('Payment Notification Error', error.toString());
    return ContentService.createTextOutput('ERROR');
  }
}

/**
 * 處理付款返回
 */
function handlePaymentReturn(e) {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <title>付款結果</title>
      <meta charset="utf-8">
      <style>
        body {
          font-family: 'Microsoft JhengHei', Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background: white;
          padding: 40px;
          border-radius: 15px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
        }
        .success {
          color: #27ae60;
          font-size: 24px;
          margin: 20px 0;
        }
        .info {
          background: #d1ecf1;
          border: 1px solid #bee5eb;
          color: #0c5460;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
        }
        .button {
          display: inline-block;
          background: #3498db;
          color: white;
          padding: 12px 24px;
          text-decoration: none;
          border-radius: 8px;
          margin: 10px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🏛️ 慈光圖書館</h1>
        <div class="success">
          <h2>✅ 付款處理完成</h2>
        </div>
        
        <div class="info">
          <h3>📋 處理結果</h3>
          <p>感謝您的護持！我們已收到您的付款資訊。</p>
          <p>系統正在處理您的捐款，請稍候片刻。</p>
          <p>如有任何問題，請聯繫我們。</p>
        </div>
        
        <div class="info">
          <h3>📞 聯絡資訊</h3>
          <p>電話：(02) 1234-5678</p>
          <p>信箱：<EMAIL></p>
        </div>
        
        <a href="?" class="button">返回捐款頁面</a>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html)
    .setTitle('付款結果')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}
