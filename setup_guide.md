# Google Sheets + 藍新金流整合設定指南

## 第一步：建立 Google Sheets 資料庫

### 1. 建立新的 Google Sheets
1. 前往 [Google Sheets](https://sheets.google.com)
2. 建立新的試算表
3. 將試算表命名為「慈光圖書館捐款記錄」
4. 複製試算表的 ID (URL 中 `/d/` 和 `/edit` 之間的部分)

### 2. 設定工作表結構
建立以下工作表：

#### 主工作表：donation_records
在第一列輸入以下標題：
```
timestamp | merchant_order_no | amount | donor_name | donor_email | donor_phone | payment_type | receipt_option | item_general | item_book | item_life | item_clinic | item_building | item_jewels | status | trade_info_sent | trade_sha_sent | newebpay_response | updated_at | notes
```

#### 輔助工作表：
- `notification_logs` - 通知日誌
- `operation_logs` - 操作日誌

## 第二步：設定 Google Apps Script

### 1. 建立 Google Apps Script 專案
1. 前往 [Google Apps Script](https://script.google.com)
2. 點擊「新專案」
3. 將專案命名為「捐款系統資料庫 API」

### 2. 建立腳本檔案

#### 檔案1：Code.gs (主要資料庫操作)
```javascript
// 將 google_apps_script_database.js 的內容複製到這裡
// 記得修改 CONFIG.SPREADSHEET_ID 為您的 Google Sheets ID
```

#### 檔案2：NotifyHandler.gs (通知處理)
```javascript
// 將 google_apps_script_notify.js 的內容複製到這裡
// 記得修改所有設定值
```

### 3. 部署為 Web App
1. 在 Google Apps Script 中點擊「部署」→「新增部署作業」
2. 類型選擇「網頁應用程式」
3. 說明：「捐款系統 API」
4. 執行身分：「我」
5. 存取權限：「任何人」(重要：藍新金流需要能夠存取)
6. 點擊「部署」
7. 複製 Web App URL

## 第三步：設定藍新金流通知

### 1. 更新 PHP 設定檔案

在 `process_donation_sheets.php` 中：
```php
// 更新 Google Sheets 設定
$GOOGLE_SHEETS_CONFIG = [
    'spreadsheet_id' => '您的Google Sheets ID',
    'sheet_name' => 'donation_records'
];

// 更新通知 URL
$notifyURL = '您的Google Apps Script Web App URL';
```

在 `google_sheets_helper.php` 中：
```php
// 更新 Web App URL
$webAppUrl = '您的Google Apps Script Web App URL';
```

### 2. 測試設定
1. 上傳所有 PHP 檔案到您的虛擬主機
2. 訪問測試頁面確認連接正常
3. 執行小額測試交易

## 第四步：權限和安全設定

### 1. Google Sheets 權限
- 確保 Google Apps Script 有權限存取您的 Google Sheets
- 設定適當的共用權限給相關人員

### 2. 安全考量
- 在 Google Apps Script 中加入 IP 白名單驗證
- 定期檢查存取日誌
- 設定資料備份機制

## 第五步：測試流程

### 1. 基本功能測試
```bash
# 測試 Google Apps Script API
curl "您的Web App URL?test=true"

# 測試新增記錄
curl -X POST "您的Web App URL" \
  -d "action=add_record&merchant_order_no=TEST123&amount=100&donor_name=測試用戶"
```

### 2. 完整流程測試
1. 填寫捐款表單
2. 確認資料寫入 Google Sheets
3. 模擬藍新金流通知
4. 確認狀態更新

## 第六步：監控和維護

### 1. 日誌監控
- 定期檢查 `notification_logs` 工作表
- 監控 `operation_logs` 工作表
- 檢查 PHP 錯誤日誌檔案

### 2. 資料備份
- 設定 Google Sheets 自動備份
- 定期匯出重要資料
- 建立災難復原計畫

## 常見問題解決

### Q1: Google Apps Script 無法接收 POST 請求
**解決方案：**
- 確認部署設定中存取權限為「任何人」
- 檢查 Web App URL 是否正確
- 確認 doPost 函數名稱正確

### Q2: 資料無法寫入 Google Sheets
**解決方案：**
- 檢查 SPREADSHEET_ID 是否正確
- 確認工作表名稱拼寫正確
- 檢查 Google Apps Script 權限

### Q3: 藍新金流通知無法送達
**解決方案：**
- 使用 webhook.site 測試通知
- 檢查免費主機的限制
- 考慮使用 Google Apps Script 作為中介

### Q4: 解密失敗
**解決方案：**
- 確認金鑰和 IV 正確
- 檢查資料格式
- 使用 PHP 解密服務協助

## 進階功能

### 1. 自動化報表
- 使用 Google Sheets 公式建立統計報表
- 設定條件格式化
- 建立圖表和儀表板

### 2. 通知機制
- 設定 Gmail 自動通知
- 整合 LINE Notify
- 建立 Slack 通知

### 3. 資料分析
- 使用 Google Data Studio 建立報表
- 匯出資料到其他分析工具
- 建立自動化分析腳本

## 支援和協助

如果在設定過程中遇到問題：
1. 檢查所有設定值是否正確
2. 查看相關日誌檔案
3. 測試各個組件的連接性
4. 確認權限設定正確

這個解決方案的優勢：
- ✅ 無需額外主機費用
- ✅ 企業級穩定性和安全性
- ✅ 即時協作和查看
- ✅ 自動備份和版本控制
- ✅ 易於擴展和維護
