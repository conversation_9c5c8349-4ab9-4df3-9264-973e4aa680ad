<?php
// return_url.php - 藍新金流付款完成後的返回頁面

// === 藍新金流設定 ===
$key = "vTxBcUTfd54kxzh01qborPcL5kJtMoxE";
$iv = "PEEDYyAVozYO6rDC";
$mid = "MS3757703701";

// 記錄返回資料
$logFile = 'return_url_log.txt';
$logMessage = "=== Return URL accessed ===\n";
$logMessage .= "Timestamp: " . date('Y-m-d H:i:s') . "\n";
$logMessage .= "Request Method: " . $_SERVER['REQUEST_METHOD'] . "\n";
$logMessage .= "GET Data: " . print_r($_GET, true) . "\n";
$logMessage .= "POST Data: " . print_r($_POST, true) . "\n";
$logMessage .= "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
$logMessage .= "Remote IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\n";

// 處理返回的資料
$paymentResult = null;
$errorMessage = '';

try {
    // 檢查是否有 TradeInfo 和 TradeSha
    $tradeInfo = $_POST['TradeInfo'] ?? $_GET['TradeInfo'] ?? '';
    $tradeSha = $_POST['TradeSha'] ?? $_GET['TradeSha'] ?? '';
    
    if (empty($tradeInfo) || empty($tradeSha)) {
        throw new Exception('Missing TradeInfo or TradeSha');
    }
    
    $logMessage .= "TradeInfo: " . $tradeInfo . "\n";
    $logMessage .= "TradeSha: " . $tradeSha . "\n";
    
    // 驗證 SHA-256 簽章
    $expectedSha = strtoupper(hash("sha256", "HashKey=" . $key . "&" . $tradeInfo . "&HashIV=" . $iv));
    
    if ($expectedSha !== strtoupper($tradeSha)) {
        throw new Exception('SHA verification failed');
    }
    
    // 解密 TradeInfo
    $encryptedData = hex2bin($tradeInfo);
    if ($encryptedData === false) {
        throw new Exception('Invalid hex data');
    }
    
    $decryptedData = openssl_decrypt($encryptedData, "AES-256-CBC", $key, OPENSSL_RAW_DATA, $iv);
    if ($decryptedData === false) {
        throw new Exception('Decryption failed');
    }
    
    // 解析參數
    parse_str($decryptedData, $paymentResult);
    
    $logMessage .= "Decrypted Data: " . print_r($paymentResult, true) . "\n";
    
} catch (Exception $e) {
    $errorMessage = $e->getMessage();
    $logMessage .= "Error: " . $errorMessage . "\n";
}

$logMessage .= "=== End Return URL ===\n\n";
file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

// 決定顯示內容
$isSuccess = $paymentResult && isset($paymentResult['Status']) && $paymentResult['Status'] === 'SUCCESS';
$merchantOrderNo = $paymentResult['MerchantOrderNo'] ?? 'Unknown';
$amount = $paymentResult['Amt'] ?? 'Unknown';
$tradeNo = $paymentResult['TradeNo'] ?? 'N/A';
$paymentType = $paymentResult['PaymentType'] ?? 'N/A';
$payTime = $paymentResult['PayTime'] ?? 'N/A';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>捐款結果 - 慈光圖書館</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .message {
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        
        .detail-value {
            color: #212529;
        }
        
        .button {
            display: inline-block;
            padding: 12px 30px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }
        
        .button.secondary {
            background: #6c757d;
        }
        
        .button.secondary:hover {
            background: #545b62;
        }
        
        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if ($isSuccess): ?>
            <!-- 付款成功 -->
            <div class="icon success">✅</div>
            <div class="title success">捐款成功！</div>
            <div class="message">
                感謝您對慈光圖書館的護持！<br>
                您的愛心捐款已成功完成，我們將善用每一分善款。
            </div>
            
            <div class="details">
                <div class="detail-row">
                    <span class="detail-label">訂單編號：</span>
                    <span class="detail-value"><?php echo htmlspecialchars($merchantOrderNo); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">捐款金額：</span>
                    <span class="detail-value">NT$ <?php echo htmlspecialchars($amount); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">交易編號：</span>
                    <span class="detail-value"><?php echo htmlspecialchars($tradeNo); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">付款方式：</span>
                    <span class="detail-value"><?php echo htmlspecialchars($paymentType); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">付款時間：</span>
                    <span class="detail-value"><?php echo htmlspecialchars($payTime); ?></span>
                </div>
            </div>
            
            <div class="message">
                📧 捐款收據將依您選擇的方式寄發<br>
                📞 如有任何問題，請聯繫我們
            </div>
            
        <?php elseif ($paymentResult && isset($paymentResult['Status'])): ?>
            <!-- 付款失敗 -->
            <div class="icon error">❌</div>
            <div class="title error">付款未完成</div>
            <div class="message">
                很抱歉，您的付款未能成功完成。<br>
                狀態：<?php echo htmlspecialchars($paymentResult['Status']); ?><br>
                <?php if (isset($paymentResult['Message'])): ?>
                    訊息：<?php echo htmlspecialchars($paymentResult['Message']); ?>
                <?php endif; ?>
            </div>
            
            <?php if ($merchantOrderNo !== 'Unknown'): ?>
            <div class="details">
                <div class="detail-row">
                    <span class="detail-label">訂單編號：</span>
                    <span class="detail-value"><?php echo htmlspecialchars($merchantOrderNo); ?></span>
                </div>
                <?php if ($amount !== 'Unknown'): ?>
                <div class="detail-row">
                    <span class="detail-label">金額：</span>
                    <span class="detail-value">NT$ <?php echo htmlspecialchars($amount); ?></span>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
        <?php else: ?>
            <!-- 資料錯誤 -->
            <div class="icon error">⚠️</div>
            <div class="title error">資料處理錯誤</div>
            <div class="message">
                無法處理付款結果資料。<br>
                錯誤訊息：<?php echo htmlspecialchars($errorMessage); ?>
            </div>
        <?php endif; ?>
        
        <!-- 操作按鈕 -->
        <div style="margin-top: 30px;">
            <?php if (!$isSuccess): ?>
                <a href="donate.html" class="button">重新捐款</a>
            <?php endif; ?>
            <a href="https://www.example.com" class="button secondary">返回首頁</a>
        </div>
        
        <!-- 頁尾資訊 -->
        <div class="footer">
            <p>財團法人慈光圖書館</p>
            <p>如有疑問請聯繫：(02) 1234-5678</p>
            <p>Email: <EMAIL></p>
        </div>
    </div>
    
    <?php if ($isSuccess): ?>
    <!-- 成功時的額外 JavaScript -->
    <script>
        // 可以在這裡加入 Google Analytics 或其他追蹤代碼
        console.log('Donation completed successfully');
        
        // 自動跳轉（可選）
        // setTimeout(function() {
        //     window.location.href = 'https://www.example.com';
        // }, 10000); // 10秒後跳轉
    </script>
    <?php endif; ?>
</body>
</html>
