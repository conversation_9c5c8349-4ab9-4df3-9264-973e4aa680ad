<?php
// test_integration.php - 完整的整合測試腳本

// 設定
$GOOGLE_APPS_SCRIPT_URL = 'https://script.google.com/macros/s/您的Google Apps Script ID/exec';
$TEST_SPREADSHEET_ID = '您的Google Sheets ID';

// 測試資料
$testData = [
    'merchant_order_no' => 'TEST' . time(),
    'amount' => 1000,
    'donor_name' => '測試捐款人',
    'donor_email' => '<EMAIL>',
    'donor_phone' => '0912345678',
    'payment_type' => 'CREDIT',
    'receipt_option' => 'monthly_receipt',
    'item_general' => 500,
    'item_book' => 300,
    'item_life' => 200,
    'item_clinic' => 0,
    'item_building' => 0,
    'item_jewels' => 0,
    'status' => 'Pending'
];

echo "<h1>Google Sheets + 藍新金流整合測試</h1>\n";
echo "<pre>\n";

// 測試 1: 基本連接測試
echo "=== 測試 1: 基本連接測試 ===\n";
$testUrl = $GOOGLE_APPS_SCRIPT_URL . '?test=true';
$response = file_get_contents($testUrl);

if ($response) {
    echo "✅ Google Apps Script 連接成功\n";
    echo "回應: " . $response . "\n";
} else {
    echo "❌ Google Apps Script 連接失敗\n";
    echo "請檢查 URL 設定\n";
}

echo "\n";

// 測試 2: 新增記錄測試
echo "=== 測試 2: 新增記錄測試 ===\n";

$postData = array_merge($testData, [
    'action' => 'add_record',
    'spreadsheet_id' => $TEST_SPREADSHEET_ID,
    'sheet_name' => 'donation_records',
    'timestamp' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
]);

$result = sendPostRequest($GOOGLE_APPS_SCRIPT_URL, $postData);

if ($result['success']) {
    echo "✅ 記錄新增成功\n";
    echo "回應: " . $result['response'] . "\n";
    
    // 測試 3: 查詢記錄測試
    echo "\n=== 測試 3: 查詢記錄測試 ===\n";
    
    $queryUrl = $GOOGLE_APPS_SCRIPT_URL . '?' . http_build_query([
        'action' => 'get_record',
        'merchant_order_no' => $testData['merchant_order_no']
    ]);
    
    $queryResponse = file_get_contents($queryUrl);
    
    if ($queryResponse) {
        echo "✅ 記錄查詢成功\n";
        echo "回應: " . $queryResponse . "\n";
        
        // 測試 4: 更新狀態測試
        echo "\n=== 測試 4: 更新狀態測試 ===\n";
        
        $updateData = [
            'action' => 'update_status',
            'merchant_order_no' => $testData['merchant_order_no'],
            'status' => 'Completed',
            'newebpay_response' => json_encode(['Status' => 'SUCCESS', 'Message' => 'Test payment']),
            'spreadsheet_id' => $TEST_SPREADSHEET_ID,
            'sheet_name' => 'donation_records'
        ];
        
        $updateResult = sendPostRequest($GOOGLE_APPS_SCRIPT_URL, $updateData);
        
        if ($updateResult['success']) {
            echo "✅ 狀態更新成功\n";
            echo "回應: " . $updateResult['response'] . "\n";
        } else {
            echo "❌ 狀態更新失敗\n";
            echo "錯誤: " . $updateResult['error'] . "\n";
        }
        
    } else {
        echo "❌ 記錄查詢失敗\n";
    }
    
} else {
    echo "❌ 記錄新增失敗\n";
    echo "錯誤: " . $result['error'] . "\n";
}

echo "\n";

// 測試 5: 藍新金流解密服務測試
echo "=== 測試 5: 藍新金流解密服務測試 ===\n";

// 模擬加密資料 (實際使用時會是真實的加密資料)
$mockTradeInfo = bin2hex('MerchantID=MS3757703701&Status=SUCCESS&MerchantOrderNo=' . $testData['merchant_order_no'] . '&Amt=1000');
$mockKey = "vTxBcUTfd54kxzh01qborPcL5kJtMoxE";
$mockIv = "PEEDYyAVozYO6rDC";

$decryptData = [
    'tradeInfo' => $mockTradeInfo,
    'key' => $mockKey,
    'iv' => $mockIv
];

$decryptResult = sendPostRequest('https://amtbtc.infy.uk/donate/decrypt_service.php', $decryptData);

if ($decryptResult['success']) {
    echo "✅ 解密服務測試成功\n";
    echo "回應: " . $decryptResult['response'] . "\n";
} else {
    echo "❌ 解密服務測試失敗\n";
    echo "錯誤: " . $decryptResult['error'] . "\n";
    echo "注意: 這可能是因為 decrypt_service.php 尚未上傳或設定\n";
}

echo "\n";

// 測試 6: 完整流程模擬
echo "=== 測試 6: 完整流程模擬 ===\n";

echo "模擬步驟:\n";
echo "1. 用戶填寫捐款表單 ✅\n";
echo "2. 資料寫入 Google Sheets ✅\n";
echo "3. 導向藍新金流 (略過實際付款)\n";
echo "4. 接收付款通知 (模擬)\n";
echo "5. 更新付款狀態 ✅\n";

echo "\n完整流程測試完成！\n";

// 測試 7: 效能測試
echo "\n=== 測試 7: 效能測試 ===\n";

$startTime = microtime(true);

// 連續新增 5 筆測試記錄
for ($i = 1; $i <= 5; $i++) {
    $perfTestData = array_merge($testData, [
        'action' => 'add_record',
        'merchant_order_no' => 'PERF_TEST_' . time() . '_' . $i,
        'donor_name' => '效能測試用戶 ' . $i,
        'spreadsheet_id' => $TEST_SPREADSHEET_ID,
        'sheet_name' => 'donation_records',
        'timestamp' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    $perfResult = sendPostRequest($GOOGLE_APPS_SCRIPT_URL, $perfTestData);
    
    if ($perfResult['success']) {
        echo "✅ 效能測試 $i/5 成功\n";
    } else {
        echo "❌ 效能測試 $i/5 失敗\n";
    }
    
    // 避免過於頻繁的請求
    usleep(500000); // 0.5 秒延遲
}

$endTime = microtime(true);
$totalTime = round($endTime - $startTime, 2);

echo "效能測試完成，總耗時: {$totalTime} 秒\n";
echo "平均每筆記錄耗時: " . round($totalTime / 5, 2) . " 秒\n";

echo "\n";

// 總結
echo "=== 測試總結 ===\n";
echo "請檢查您的 Google Sheets 是否有新增的測試資料\n";
echo "如果所有測試都通過，您的系統已準備好進行實際部署\n";
echo "\n下一步:\n";
echo "1. 在藍新金流後台設定正確的 NotifyURL\n";
echo "2. 進行小額真實交易測試\n";
echo "3. 監控日誌檔案確保一切正常\n";

echo "</pre>\n";

/**
 * 發送 POST 請求的輔助函數
 */
function sendPostRequest($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($httpCode === 200 && !$error) {
        return ['success' => true, 'response' => $response];
    } else {
        return ['success' => false, 'error' => "HTTP $httpCode: $error"];
    }
}
?>
