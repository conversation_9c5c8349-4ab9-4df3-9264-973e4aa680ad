/**
 * 調試版本 - Code.gs
 * 用於診斷問題的簡化版本
 */

// === 系統設定 ===
const DEBUG_CONFIG = {
  SPREADSHEET_ID: '1Qos3rkD7kukxj7qbiJmBij6Ws0lKbsCT0uISNbvAqUc',
  SHEET_NAME: 'donation_records',
  LOG_SHEET_NAME: 'system_logs'
};

/**
 * 處理 GET 請求
 */
function doGet(e) {
  try {
    const action = e.parameter.action || 'form';
    
    // 記錄請求
    console.log('GET Request:', action);
    debugLog('GET Request', { action: action });
    
    switch (action) {
      case 'form':
        return showDebugForm();
      case 'test':
        return handleDebugTest();
      default:
        return showDebugForm();
    }
    
  } catch (error) {
    console.error('GET Error:', error);
    debugLog('GET Error', error.toString());
    return createDebugError('GET 錯誤: ' + error.toString());
  }
}

/**
 * 處理 POST 請求
 */
function doPost(e) {
  try {
    console.log('POST Request received');
    debugLog('POST Request', 'Started processing');
    
    // 記錄所有接收到的參數
    const params = {};
    Object.keys(e.parameters).forEach(key => {
      params[key] = e.parameters[key][0];
    });
    
    debugLog('POST Parameters', params);
    
    // 簡單驗證
    if (!params.donor_name) {
      debugLog('Validation Error', 'Missing donor_name');
      return createDebugError('缺少捐款人姓名');
    }
    
    if (!params.donor_email) {
      debugLog('Validation Error', 'Missing donor_email');
      return createDebugError('缺少電子郵件');
    }
    
    // 計算金額
    const amount = calculateDebugAmount(params);
    debugLog('Amount Calculated', { amount: amount });
    
    if (amount <= 0) {
      debugLog('Validation Error', 'Invalid amount');
      return createDebugError('捐款金額必須大於零');
    }
    
    // 生成訂單編號
    const orderNo = 'DEBUG' + Date.now();
    debugLog('Order Generated', { orderNo: orderNo });
    
    // 嘗試儲存記錄
    const saveResult = saveDebugRecord(params, orderNo, amount);
    debugLog('Save Result', saveResult);
    
    // 返回成功頁面
    return createDebugSuccess(orderNo, amount);
    
  } catch (error) {
    console.error('POST Error:', error);
    debugLog('POST Error', error.toString());
    return createDebugError('POST 錯誤: ' + error.toString());
  }
}

/**
 * 顯示調試表單
 */
function showDebugForm() {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>調試版捐款表單</title>
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .submit-btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; width: 100%; font-size: 16px; }
        .submit-btn:hover { background: #0056b3; }
        .debug-info { background: #e7f3ff; border: 1px solid #b3d9ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #ffe6e6; border: 1px solid #ffb3b3; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .total { background: #e8f5e8; border: 1px solid #b3e6b3; padding: 10px; border-radius: 4px; margin: 10px 0; font-weight: bold; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🔧 調試版捐款表單</h1>
        
        <div class="debug-info">
          <strong>調試資訊：</strong><br>
          時間：${new Date().toLocaleString('zh-TW')}<br>
          版本：調試版 v1.0<br>
          狀態：等待表單提交
        </div>
        
        <div id="errorMessage" class="error" style="display: none;"></div>
        
        <form id="debugForm" method="post">
          <div class="form-group">
            <label for="donor_name">捐款人姓名 *</label>
            <input type="text" id="donor_name" name="donor_name" required>
          </div>
          
          <div class="form-group">
            <label for="donor_email">電子郵件 *</label>
            <input type="email" id="donor_email" name="donor_email" required>
          </div>
          
          <div class="form-group">
            <label for="donor_phone">聯絡電話</label>
            <input type="tel" id="donor_phone" name="donor_phone">
          </div>
          
          <div class="form-group">
            <label for="amount_general">一般經費</label>
            <input type="number" id="amount_general" name="item_amount_general_expenses" min="0" value="0" onchange="calculateTotal()">
          </div>
          
          <div class="form-group">
            <label for="amount_book">助印弘法</label>
            <input type="number" id="amount_book" name="item_amount_book_printing" min="0" value="0" onchange="calculateTotal()">
          </div>
          
          <div class="total">
            總金額：NT$ <span id="totalAmount">0</span>
          </div>
          
          <button type="submit" class="submit-btn" id="submitBtn">提交測試</button>
        </form>
      </div>
      
      <script>
        function calculateTotal() {
          const general = parseInt(document.getElementById('amount_general').value) || 0;
          const book = parseInt(document.getElementById('amount_book').value) || 0;
          const total = general + book;
          document.getElementById('totalAmount').textContent = total.toLocaleString();
        }
        
        function showError(message) {
          const errorDiv = document.getElementById('errorMessage');
          errorDiv.textContent = message;
          errorDiv.style.display = 'block';
        }
        
        document.getElementById('debugForm').addEventListener('submit', function(e) {
          console.log('Form submit triggered');
          
          // 基本驗證
          const name = document.getElementById('donor_name').value.trim();
          const email = document.getElementById('donor_email').value.trim();
          const total = parseInt(document.getElementById('totalAmount').textContent.replace(/,/g, ''));
          
          if (!name) {
            e.preventDefault();
            showError('請輸入捐款人姓名');
            return false;
          }
          
          if (!email) {
            e.preventDefault();
            showError('請輸入電子郵件');
            return false;
          }
          
          if (total <= 0) {
            e.preventDefault();
            showError('請輸入捐款金額');
            return false;
          }
          
          // 顯示提交狀態
          const submitBtn = document.getElementById('submitBtn');
          submitBtn.textContent = '處理中...';
          submitBtn.disabled = true;
          
          console.log('Form validation passed, submitting...');
          return true;
        });
        
        // 初始化
        calculateTotal();
        console.log('Debug form loaded');
      </script>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html)
    .setTitle('調試版捐款表單')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * 計算調試金額
 */
function calculateDebugAmount(params) {
  const general = parseInt(params.item_amount_general_expenses) || 0;
  const book = parseInt(params.item_amount_book_printing) || 0;
  return general + book;
}

/**
 * 儲存調試記錄
 */
function saveDebugRecord(params, orderNo, amount) {
  try {
    const spreadsheet = SpreadsheetApp.openById(DEBUG_CONFIG.SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(DEBUG_CONFIG.SHEET_NAME);
    
    const record = [
      new Date().toLocaleString('zh-TW'),
      orderNo,
      amount,
      params.donor_name || '',
      params.donor_email || '',
      params.donor_phone || '',
      'DEBUG',  // payment_type
      'debug_test',  // receipt_option
      parseInt(params.item_amount_general_expenses) || 0,
      parseInt(params.item_amount_book_printing) || 0,
      0, 0, 0, 0,  // 其他項目
      'Debug Test',
      '', '', '',  // 藍新相關欄位
      new Date().toLocaleString('zh-TW'),
      'Debug record'
    ];
    
    sheet.appendRow(record);
    
    return { success: true, message: 'Record saved successfully' };
    
  } catch (error) {
    return { success: false, error: error.toString() };
  }
}

/**
 * 調試日誌
 */
function debugLog(type, data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(DEBUG_CONFIG.SPREADSHEET_ID);
    const logSheet = spreadsheet.getSheetByName(DEBUG_CONFIG.LOG_SHEET_NAME);
    
    logSheet.appendRow([
      new Date(),
      'DEBUG: ' + type,
      typeof data === 'object' ? JSON.stringify(data) : data,
      'Debug Session'
    ]);
    
  } catch (error) {
    console.error('Debug logging failed:', error);
  }
}

/**
 * 調試測試
 */
function handleDebugTest() {
  try {
    const spreadsheet = SpreadsheetApp.openById(DEBUG_CONFIG.SPREADSHEET_ID);
    
    const result = {
      status: 'DEBUG_OK',
      timestamp: new Date().toISOString(),
      spreadsheet_name: spreadsheet.getName(),
      sheets: spreadsheet.getSheets().map(sheet => sheet.getName()),
      message: '調試測試成功'
    };
    
    debugLog('Debug Test', result);
    
    return ContentService
      .createTextOutput(JSON.stringify(result, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    const errorResult = {
      status: 'DEBUG_ERROR',
      error: error.toString()
    };
    
    return ContentService
      .createTextOutput(JSON.stringify(errorResult, null, 2))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 建立調試成功頁面
 */
function createDebugSuccess(orderNo, amount) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>調試成功</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; text-align: center; }
        .success { color: green; font-size: 24px; margin: 20px 0; }
        .info { background: #e8f5e8; border: 1px solid #b3e6b3; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .button { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 10px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="success">✅ 調試測試成功！</div>
        
        <div class="info">
          <h3>處理結果</h3>
          <p><strong>訂單編號：</strong>${orderNo}</p>
          <p><strong>金額：</strong>NT$ ${amount}</p>
          <p><strong>時間：</strong>${new Date().toLocaleString('zh-TW')}</p>
        </div>
        
        <div class="info">
          <h3>系統狀態</h3>
          <p>✅ 表單驗證正常</p>
          <p>✅ POST 請求處理正常</p>
          <p>✅ 資料庫寫入正常</p>
          <p>✅ 頁面渲染正常</p>
        </div>
        
        <a href="?" class="button">返回表單</a>
        <a href="?test=true" class="button">系統測試</a>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}

/**
 * 建立調試錯誤頁面
 */
function createDebugError(message) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>調試錯誤</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; text-align: center; }
        .error { color: red; font-size: 18px; margin: 20px 0; }
        .info { background: #ffe6e6; border: 1px solid #ffb3b3; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .button { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 10px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="error">❌ 調試錯誤</div>
        
        <div class="info">
          <h3>錯誤訊息</h3>
          <p>${message}</p>
          <p><strong>時間：</strong>${new Date().toLocaleString('zh-TW')}</p>
        </div>
        
        <a href="?" class="button">返回表單</a>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}
