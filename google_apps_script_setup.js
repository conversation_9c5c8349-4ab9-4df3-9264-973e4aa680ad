/**
 * Google Apps Script - 系統初始化腳本
 * 這個腳本會自動建立所有需要的工作表和欄位結構
 * 檔案名稱：Setup.gs
 */

// === 設定區域 ===
const SETUP_CONFIG = {
  // 請在這裡填入您的 Google Sheets ID
  SPREADSHEET_ID: '請替換為您的 Google Sheets ID',
  
  // 工作表名稱
  SHEETS: {
    DONATIONS: 'donation_records',
    LOGS: 'system_logs',
    NOTIFY_LOGS: 'notification_logs',
    PAYMENT_METHODS: 'payment_methods',
    DONATION_ITEMS: 'donation_items',
    DAILY_SUMMARY: 'daily_summary'
  }
};

/**
 * 主要初始化函數 - 執行這個函數來建立所有工作表
 */
function initializeSystem() {
  try {
    console.log('開始初始化慈光圖書館捐款系統...');
    
    // 開啟試算表
    const spreadsheet = SpreadsheetApp.openById(SETUP_CONFIG.SPREADSHEET_ID);
    console.log('成功開啟試算表：' + spreadsheet.getName());
    
    // 建立各個工作表
    createDonationRecordsSheet(spreadsheet);
    createSystemLogsSheet(spreadsheet);
    createNotificationLogsSheet(spreadsheet);
    createPaymentMethodsSheet(spreadsheet);
    createDonationItemsSheet(spreadsheet);
    createDailySummarySheet(spreadsheet);
    
    // 設定工作表格式
    formatAllSheets(spreadsheet);
    
    console.log('系統初始化完成！');
    
    // 返回成功訊息
    return {
      success: true,
      message: '慈光圖書館捐款系統初始化完成',
      spreadsheetUrl: spreadsheet.getUrl(),
      sheets: Object.values(SETUP_CONFIG.SHEETS)
    };
    
  } catch (error) {
    console.error('初始化失敗：', error);
    return {
      success: false,
      error: error.toString()
    };
  }
}

/**
 * 建立主要的捐款記錄工作表
 */
function createDonationRecordsSheet(spreadsheet) {
  const sheetName = SETUP_CONFIG.SHEETS.DONATIONS;
  
  // 檢查工作表是否已存在
  let sheet = spreadsheet.getSheetByName(sheetName);
  if (sheet) {
    console.log(`工作表 ${sheetName} 已存在，跳過建立`);
    return sheet;
  }
  
  // 建立新工作表
  sheet = spreadsheet.insertSheet(sheetName);
  console.log(`建立工作表：${sheetName}`);
  
  // 設定標題列
  const headers = [
    'timestamp',           // A - 時間戳記
    'merchant_order_no',   // B - 商店訂單編號
    'amount',             // C - 捐款金額
    'donor_name',         // D - 捐款人姓名
    'donor_email',        // E - 捐款人信箱
    'donor_phone',        // F - 捐款人電話
    'payment_type',       // G - 付款方式
    'receipt_option',     // H - 收據選項
    'item_general',       // I - 一般經費金額
    'item_book',          // J - 助印弘法金額
    'item_life',          // K - 護生關懷金額
    'item_clinic',        // L - 義診慈善金額
    'item_building',      // M - 建築修繕金額
    'item_jewels',        // N - 供養三寶金額
    'status',             // O - 付款狀態
    'trade_info_sent',    // P - 發送的加密資料
    'trade_sha_sent',     // Q - 發送的雜湊值
    'newebpay_response',  // R - 藍新回應資料
    'updated_at',         // S - 最後更新時間
    'notes'               // T - 備註
  ];
  
  // 寫入標題列
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // 設定標題列格式
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#4285f4');
  headerRange.setFontColor('white');
  
  // 凍結標題列
  sheet.setFrozenRows(1);
  
  // 設定欄寬
  sheet.setColumnWidth(1, 150);  // timestamp
  sheet.setColumnWidth(2, 180);  // merchant_order_no
  sheet.setColumnWidth(3, 100);  // amount
  sheet.setColumnWidth(4, 120);  // donor_name
  sheet.setColumnWidth(5, 200);  // donor_email
  sheet.setColumnWidth(15, 100); // status
  
  return sheet;
}

/**
 * 建立系統日誌工作表
 */
function createSystemLogsSheet(spreadsheet) {
  const sheetName = SETUP_CONFIG.SHEETS.LOGS;
  
  let sheet = spreadsheet.getSheetByName(sheetName);
  if (sheet) {
    console.log(`工作表 ${sheetName} 已存在，跳過建立`);
    return sheet;
  }
  
  sheet = spreadsheet.insertSheet(sheetName);
  console.log(`建立工作表：${sheetName}`);
  
  const headers = ['timestamp', 'type', 'data', 'user', 'ip_address'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // 格式化標題
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#34a853');
  headerRange.setFontColor('white');
  
  sheet.setFrozenRows(1);
  sheet.setColumnWidth(1, 150);
  sheet.setColumnWidth(2, 120);
  sheet.setColumnWidth(3, 300);
  
  return sheet;
}

/**
 * 建立通知日誌工作表
 */
function createNotificationLogsSheet(spreadsheet) {
  const sheetName = SETUP_CONFIG.SHEETS.NOTIFY_LOGS;
  
  let sheet = spreadsheet.getSheetByName(sheetName);
  if (sheet) {
    console.log(`工作表 ${sheetName} 已存在，跳過建立`);
    return sheet;
  }
  
  sheet = spreadsheet.insertSheet(sheetName);
  console.log(`建立工作表：${sheetName}`);
  
  const headers = ['timestamp', 'type', 'trade_info', 'trade_sha', 'status', 'response_data', 'error_message'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // 格式化標題
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#ff9800');
  headerRange.setFontColor('white');
  
  sheet.setFrozenRows(1);
  
  return sheet;
}

/**
 * 建立付款方式對照表
 */
function createPaymentMethodsSheet(spreadsheet) {
  const sheetName = SETUP_CONFIG.SHEETS.PAYMENT_METHODS;
  
  let sheet = spreadsheet.getSheetByName(sheetName);
  if (sheet) {
    console.log(`工作表 ${sheetName} 已存在，跳過建立`);
    return sheet;
  }
  
  sheet = spreadsheet.insertSheet(sheetName);
  console.log(`建立工作表：${sheetName}`);
  
  const headers = ['payment_code', 'payment_name_zh', 'payment_name_en', 'enabled', 'description'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // 預設付款方式資料
  const paymentMethods = [
    ['CREDIT', '信用卡', 'Credit Card', 'TRUE', '信用卡付款'],
    ['UNIONPAY', '銀聯卡', 'UnionPay', 'TRUE', '銀聯卡付款'],
    ['CVS', '超商條碼', 'CVS Barcode', 'TRUE', '超商條碼付款'],
    ['TAIWANPAY', '台灣Pay', 'Taiwan Pay', 'TRUE', '台灣Pay行動支付'],
    ['LINEPAY', 'LINE Pay', 'LINE Pay', 'TRUE', 'LINE Pay付款'],
    ['GOOGLEPAY', 'Google Pay', 'Google Pay', 'TRUE', 'Google Pay付款'],
    ['WEBATM', 'WebATM', 'WebATM', 'TRUE', '網路ATM'],
    ['VACC', 'ATM轉帳', 'ATM Transfer', 'TRUE', 'ATM轉帳付款'],
    ['FOREIGN', '國外卡', 'Foreign Card', 'TRUE', '國外信用卡']
  ];
  
  sheet.getRange(2, 1, paymentMethods.length, paymentMethods[0].length).setValues(paymentMethods);
  
  // 格式化
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#9c27b0');
  headerRange.setFontColor('white');
  
  sheet.setFrozenRows(1);
  
  return sheet;
}

/**
 * 建立捐款項目對照表
 */
function createDonationItemsSheet(spreadsheet) {
  const sheetName = SETUP_CONFIG.SHEETS.DONATION_ITEMS;
  
  let sheet = spreadsheet.getSheetByName(sheetName);
  if (sheet) {
    console.log(`工作表 ${sheetName} 已存在，跳過建立`);
    return sheet;
  }
  
  sheet = spreadsheet.insertSheet(sheetName);
  console.log(`建立工作表：${sheetName}`);
  
  const headers = ['item_code', 'item_name', 'description', 'enabled', 'sort_order'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // 預設捐款項目
  const donationItems = [
    ['general', '一般經費', '圖書館日常營運經費', 'TRUE', '1'],
    ['book', '助印弘法', '佛法書籍印製與流通', 'TRUE', '2'],
    ['life', '護生關懷助念', '護生活動與臨終關懷', 'TRUE', '3'],
    ['clinic', '義診慈善救濟', '免費醫療與慈善救助', 'TRUE', '4'],
    ['building', '建築修繕設備', '建築物維護與設備更新', 'TRUE', '5'],
    ['jewels', '供養三寶', '供養佛法僧三寶', 'TRUE', '6']
  ];
  
  sheet.getRange(2, 1, donationItems.length, donationItems[0].length).setValues(donationItems);
  
  // 格式化
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#795548');
  headerRange.setFontColor('white');
  
  sheet.setFrozenRows(1);
  
  return sheet;
}

/**
 * 建立每日統計工作表
 */
function createDailySummarySheet(spreadsheet) {
  const sheetName = SETUP_CONFIG.SHEETS.DAILY_SUMMARY;
  
  let sheet = spreadsheet.getSheetByName(sheetName);
  if (sheet) {
    console.log(`工作表 ${sheetName} 已存在，跳過建立`);
    return sheet;
  }
  
  sheet = spreadsheet.insertSheet(sheetName);
  console.log(`建立工作表：${sheetName}`);
  
  const headers = ['date', 'total_count', 'total_amount', 'success_count', 'failed_count', 'pending_count', 'notes'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // 格式化
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#607d8b');
  headerRange.setFontColor('white');
  
  sheet.setFrozenRows(1);
  
  return sheet;
}

/**
 * 格式化所有工作表
 */
function formatAllSheets(spreadsheet) {
  console.log('開始格式化工作表...');
  
  // 設定捐款記錄工作表的條件格式
  const donationSheet = spreadsheet.getSheetByName(SETUP_CONFIG.SHEETS.DONATIONS);
  if (donationSheet) {
    // 狀態欄位的條件格式
    const statusRange = donationSheet.getRange('O:O');
    
    // 成功狀態 - 綠色
    const successRule = SpreadsheetApp.newConditionalFormatRule()
      .whenTextEqualTo('Completed')
      .setBackground('#d4edda')
      .setFontColor('#155724')
      .setRanges([statusRange])
      .build();
    
    // 失敗狀態 - 紅色
    const failedRule = SpreadsheetApp.newConditionalFormatRule()
      .whenTextEqualTo('Failed')
      .setBackground('#f8d7da')
      .setFontColor('#721c24')
      .setRanges([statusRange])
      .build();
    
    // 待處理狀態 - 黃色
    const pendingRule = SpreadsheetApp.newConditionalFormatRule()
      .whenTextEqualTo('Pending')
      .setBackground('#fff3cd')
      .setFontColor('#856404')
      .setRanges([statusRange])
      .build();
    
    donationSheet.setConditionalFormatRules([successRule, failedRule, pendingRule]);
  }
  
  console.log('工作表格式化完成');
}

/**
 * 測試函數 - 檢查系統是否正確初始化
 */
function testSystemSetup() {
  try {
    const spreadsheet = SpreadsheetApp.openById(SETUP_CONFIG.SPREADSHEET_ID);
    const results = {};
    
    // 檢查每個工作表是否存在
    Object.values(SETUP_CONFIG.SHEETS).forEach(sheetName => {
      const sheet = spreadsheet.getSheetByName(sheetName);
      results[sheetName] = {
        exists: !!sheet,
        rowCount: sheet ? sheet.getLastRow() : 0,
        columnCount: sheet ? sheet.getLastColumn() : 0
      };
    });
    
    console.log('系統檢查結果：', results);
    return results;
    
  } catch (error) {
    console.error('系統檢查失敗：', error);
    return { error: error.toString() };
  }
}

/**
 * 清理函數 - 刪除所有建立的工作表（謹慎使用）
 */
function cleanupSystem() {
  try {
    const spreadsheet = SpreadsheetApp.openById(SETUP_CONFIG.SPREADSHEET_ID);
    
    Object.values(SETUP_CONFIG.SHEETS).forEach(sheetName => {
      const sheet = spreadsheet.getSheetByName(sheetName);
      if (sheet) {
        spreadsheet.deleteSheet(sheet);
        console.log(`已刪除工作表：${sheetName}`);
      }
    });
    
    console.log('系統清理完成');
    return { success: true, message: '所有工作表已刪除' };
    
  } catch (error) {
    console.error('系統清理失敗：', error);
    return { success: false, error: error.toString() };
  }
}
