# 慈光圖書館捐款系統 - 逐步部署指南

## 📋 部署前準備

### 需要準備的資訊：
- [ ] Google 帳戶（有 Google Workspace 更佳）
- [ ] 藍新金流商店帳戶和測試金鑰
- [ ] 約 30-60 分鐘的時間

## 🚀 第一階段：建立 Google Sheets 資料庫

### 步驟 1.1：建立試算表
1. 前往 [Google Sheets](https://sheets.google.com)
2. 點擊左上角的「空白」建立新試算表
3. 點擊左上角的「未命名的試算表」
4. 將名稱改為：`慈光圖書館捐款記錄`
5. 按 Enter 確認

### 步驟 1.2：取得 Sheets ID
1. 複製瀏覽器網址列中的 URL
2. 找到 `/d/` 和 `/edit` 之間的長字串
3. 例如：`https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit`
4. Sheets ID 就是：`1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms`

**請將您的 Sheets ID 記錄下來：**
```
您的 Sheets ID：_________________________________
```

## 🔧 第二階段：建立 Google Apps Script 專案

### 步驟 2.1：建立新專案
1. 前往 [Google Apps Script](https://script.google.com)
2. 點擊左上角的「新專案」
3. 將專案名稱改為：`慈光圖書館捐款系統`

### 步驟 2.2：建立初始化腳本
1. 將預設的 `Code.gs` 檔案內容全部刪除
2. 複製 `google_apps_script_setup.js` 的完整內容
3. 貼上到 `Code.gs` 中
4. 找到第 9 行：`SPREADSHEET_ID: '請替換為您的 Google Sheets ID'`
5. 將 `請替換為您的 Google Sheets ID` 替換為您的實際 Sheets ID
6. 按 `Ctrl+S` 儲存

### 步驟 2.3：執行初始化
1. 在函數選擇器中選擇 `initializeSystem`
2. 點擊「執行」按鈕（播放圖示）
3. 第一次執行會要求授權：
   - 點擊「檢閱權限」
   - 選擇您的 Google 帳戶
   - 點擊「進階」
   - 點擊「前往慈光圖書館捐款系統（不安全）」
   - 點擊「允許」
4. 等待執行完成（約 10-30 秒）
5. 檢查執行日誌，應該看到「系統初始化完成！」

### 步驟 2.4：驗證工作表建立
1. 回到您的 Google Sheets
2. 重新整理頁面
3. 確認底部有以下工作表分頁：
   - `donation_records`（主要捐款記錄）
   - `system_logs`（系統日誌）
   - `notification_logs`（通知日誌）
   - `payment_methods`（付款方式）
   - `donation_items`（捐款項目）
   - `daily_summary`（每日統計）

## 📝 第三階段：建立主要系統腳本

### 步驟 3.1：建立主控制器
1. 在 Google Apps Script 中點擊「檔案」→「新增」→「指令碼」
2. 將新檔案命名為 `MainController`
3. 複製 `google_apps_script_main.js` 的完整內容
4. 貼上並儲存
5. 找到第 9 行的 `SPREADSHEET_ID`，替換為您的 Sheets ID

### 步驟 3.2：建立付款處理器
1. 新增另一個指令碼檔案，命名為 `PaymentProcessor`
2. 複製 `google_apps_script_payment.js` 的完整內容
3. 貼上並儲存
4. 找到第 18 行的 `SPREADSHEET_ID`，替換為您的 Sheets ID

### 步驟 3.3：建立通知處理器
1. 新增指令碼檔案，命名為 `NotifyHandler`
2. 複製 `google_apps_script_notify.js` 的完整內容
3. 貼上並儲存
4. 找到第 15 行的 `SPREADSHEET_ID`，替換為您的 Sheets ID

### 步驟 3.4：建立資料庫 API
1. 新增指令碼檔案，命名為 `DatabaseAPI`
2. 複製 `google_apps_script_database.js` 的完整內容
3. 貼上並儲存
4. 找到第 9 行的 `SPREADSHEET_ID`，替換為您的 Sheets ID

## 🌐 第四階段：部署 Web 應用程式

### 步驟 4.1：部署設定
1. 在 Google Apps Script 中點擊右上角的「部署」
2. 選擇「新增部署作業」
3. 在「類型」中選擇「網頁應用程式」
4. 填寫以下資訊：
   - **說明**：`慈光圖書館捐款系統 v1.0`
   - **執行身分**：選擇「我」
   - **存取權限**：選擇「任何人」（重要！）
5. 點擊「部署」

### 步驟 4.2：取得 Web App URL
1. 部署完成後，複製「網頁應用程式 URL」
2. URL 格式：`https://script.google.com/macros/s/[長字串]/exec`

**請將您的 Web App URL 記錄下來：**
```
您的 Web App URL：_________________________________
```

## 🧪 第五階段：系統測試

### 步驟 5.1：基本連接測試
1. 在瀏覽器中開啟：`您的Web App URL?test=true`
2. 應該看到 JSON 格式的系統狀態回應
3. 確認 `spreadsheet_access: true`

### 步驟 5.2：捐款表單測試
1. 在瀏覽器中開啟：`您的Web App URL`
2. 應該看到美觀的捐款表單
3. 填寫測試資料：
   - 姓名：測試用戶
   - 信箱：<EMAIL>
   - 一般經費：1000
4. 點擊「確認捐款」
5. 應該會導向藍新金流頁面（會顯示錯誤，這是正常的）

### 步驟 5.3：資料庫檢查
1. 回到 Google Sheets
2. 檢查 `donation_records` 工作表
3. 應該看到剛才的測試記錄
4. 檢查 `system_logs` 工作表，確認有操作記錄

## 💳 第六階段：藍新金流設定

### 步驟 6.1：登入藍新金流後台
1. 前往藍新金流商店管理後台
2. 進入「系統設定」→「支付設定」

### 步驟 6.2：設定通知 URL
1. 找到「支付完成返回商店網址」設定
2. 填入：`您的Web App URL?action=return`
3. 找到「支付通知網址」設定
4. 填入：`您的Web App URL?action=notify`

### 步驟 6.3：測試環境設定
1. 確認使用測試環境金鑰
2. 進行小額測試交易
3. 檢查 Google Sheets 中的記錄更新

## 📊 第七階段：使用測試工具

### 步驟 7.1：開啟測試工具
1. 下載 `google_service_test.html`
2. 在瀏覽器中開啟此檔案
3. 填入您的 Web App URL 和 Sheets ID
4. 點擊「儲存設定」

### 步驟 7.2：執行完整測試
1. 點擊「執行完整測試」
2. 等待所有測試完成
3. 檢查測試結果，確保都是「通過」狀態

## ✅ 部署完成檢查清單

- [ ] Google Sheets 已建立並包含所有工作表
- [ ] Google Apps Script 專案已建立並包含所有腳本
- [ ] Web App 已成功部署
- [ ] 基本連接測試通過
- [ ] 捐款表單正常顯示
- [ ] 測試資料成功寫入 Google Sheets
- [ ] 藍新金流 URL 已設定
- [ ] 測試工具所有項目通過

## 🎉 恭喜！系統部署完成

您的捐款系統現在已經可以正常運作了！

**系統網址：** `您的Web App URL`

### 下一步建議：
1. 進行小額真實交易測試
2. 設定定期備份機制
3. 建立操作手冊給相關人員
4. 監控系統運作狀況

### 如果遇到問題：
1. 檢查 Google Sheets 中的 `system_logs` 工作表
2. 確認所有 Sheets ID 都正確設定
3. 確認 Web App 部署時存取權限為「任何人」
4. 使用測試工具進行診斷

## 📞 技術支援

如果在部署過程中遇到任何問題，請提供：
1. 錯誤訊息截圖
2. Google Apps Script 執行日誌
3. 您的 Sheets ID 和 Web App URL
4. 具體的操作步驟

祝您部署順利！🎊
